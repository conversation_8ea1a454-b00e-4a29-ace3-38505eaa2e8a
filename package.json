{"name": "nextchat", "private": false, "license": "mit", "scripts": {"mask": "npx tsx app/masks/build.ts", "mask:watch": "npx watch \"yarn mask\" app/masks", "dev": "concurrently -r \"yarn run mask:watch\" \"next dev\"", "build": "yarn mask && cross-env BUILD_MODE=standalone next build", "start": "next start", "lint": "next lint", "export": "yarn mask && cross-env BUILD_MODE=export BUILD_APP=1 next build", "export:dev": "concurrently -r \"yarn mask:watch\"  \"cross-env BUILD_MODE=export BUILD_APP=1 next dev\"", "app:dev": "concurrently -r \"yarn mask:watch\" \"yarn tauri dev\"", "app:build": "yarn mask && yarn tauri build", "app:clear": "yarn tauri dev", "prompts": "node ./scripts/fetch-prompts.mjs", "prepare": "husky install", "proxy-dev-disabled": "echo 'Proxy development mode has been disabled to avoid proxychains errors. Use yarn dev instead.'", "test": "node --no-warnings --experimental-vm-modules $(yarn bin jest) --watch", "test:ci": "node --no-warnings --experimental-vm-modules $(yarn bin jest) --ci"}, "dependencies": {"@fortaine/fetch-event-source": "^3.0.6", "@hello-pangea/dnd": "^16.5.0", "@modelcontextprotocol/sdk": "^1.0.4", "@next/third-parties": "^14.1.0", "@svgr/webpack": "^6.5.1", "@vercel/analytics": "^0.1.11", "@vercel/speed-insights": "^1.0.2", "axios": "^1.7.5", "clsx": "^2.1.1", "emoji-picker-react": "^4.9.2", "fuse.js": "^7.0.0", "heic2any": "^0.0.4", "html-to-image": "^1.11.11", "http-proxy-agent": "^7.0.0", "https-proxy-agent": "^7.0.2", "socks-proxy-agent": "^8.0.2", "idb-keyval": "^6.2.1", "lodash-es": "^4.17.21", "markdown-to-txt": "^2.0.1", "mermaid": "^10.6.1", "nanoid": "^5.0.3", "next": "^14.1.1", "node-fetch": "^3.3.1", "openapi-client-axios": "^7.5.5", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^8.0.7", "react-router-dom": "^6.15.0", "rehype-highlight": "^6.0.0", "rehype-katex": "^6.0.3", "remark-breaks": "^3.0.2", "remark-gfm": "^3.0.1", "remark-math": "^5.1.1", "rt-client": "https://github.com/Azure-Samples/aoai-realtime-audio-sdk/releases/download/js/v0.5.0/rt-client-0.5.0.tgz", "sass": "^1.59.2", "sharp": "^0.34.3", "spark-md5": "^3.0.2", "use-debounce": "^9.0.4", "zod": "^3.24.1", "zustand": "^4.3.8"}, "devDependencies": {"@tauri-apps/api": "^2.1.1", "@tauri-apps/cli": "1.5.11", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@types/jest": "^29.5.14", "@types/js-yaml": "4.0.9", "@types/lodash-es": "^4.17.12", "@types/node": "^20.11.30", "@types/react": "^18.2.70", "@types/react-dom": "^18.2.7", "@types/react-katex": "^3.0.0", "@types/spark-md5": "^3.0.4", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "eslint": "^8.49.0", "eslint-config-next": "13.4.19", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-unused-imports": "^3.2.0", "husky": "^8.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^13.2.2", "prettier": "^3.0.2", "ts-node": "^10.9.2", "tsx": "^4.16.0", "typescript": "5.2.2", "watch": "^1.0.2", "webpack": "^5.88.1"}, "resolutions": {"lint-staged/yaml": "^2.2.2"}, "packageManager": "yarn@1.22.19"}