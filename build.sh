#!/bin/bash

# NextChat 本地构建脚本
# 集成所有构建功能：环境检查、依赖安装、构建、打包、优化

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
AUTO_OPTIMIZE=true
AUTO_CLEANUP=true
SHOW_PROGRESS=true
COMPRESSION_LEVEL=6

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 未安装，请先安装 $1"
        exit 1
    fi
}

# 检查 Node.js 版本
check_node_version() {
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装"
        exit 1
    fi
    
    local node_version=$(node --version 2>/dev/null | sed 's/v//')
    local required_version="18.17.0"
    
    if [ -z "$node_version" ]; then
        log_error "无法获取 Node.js 版本信息"
        exit 1
    fi
    
    # 使用简单的版本比较
    local node_major=$(echo "$node_version" | cut -d. -f1)
    local node_minor=$(echo "$node_version" | cut -d. -f2)
    
    # 确保提取的版本号是数字
    if ! [[ "$node_major" =~ ^[0-9]+$ ]] || ! [[ "$node_minor" =~ ^[0-9]+$ ]]; then
        log_error "Node.js 版本格式异常: $node_version"
        exit 1
    fi
    
    if [ "$node_major" -lt 18 ] || ([ "$node_major" -eq 18 ] && [ "$node_minor" -lt 17 ]); then
        log_warning "Node.js 版本过低，当前版本: v$node_version，建议版本: >= v$required_version"
        log_warning "继续构建，但可能遇到兼容性问题"
    fi
    
    log_info "Node.js 版本检查通过: v$node_version"
}

# 智能清理函数已内联到主函数中

# 优化 dist 目录
optimize_dist() {
    if [ "$AUTO_OPTIMIZE" = "true" ] && [ -d "dist" ]; then
        log_info "优化 dist 目录..."
        
        # 记录优化前大小
        if [ "$SHOW_PROGRESS" = "true" ]; then
            local before_size=$(du -sh dist 2>/dev/null | cut -f1 || echo "未知")
            log_info "优化前大小: $before_size"
        fi
        
        # 删除构建缓存
        if [ -d "dist/cache" ]; then
            local cache_size=$(du -sh dist/cache 2>/dev/null | cut -f1 || echo "未知")
            rm -rf dist/cache
            [ "$SHOW_PROGRESS" = "true" ] && log_info "删除构建缓存: $cache_size"
        fi
        
        # 删除开发文件
        find dist -name "*.trace" -delete 2>/dev/null || true
        find dist -name "*.nft.json" -delete 2>/dev/null || true
        find dist -name "*.map" -delete 2>/dev/null || true
        find dist -name ".DS_Store" -delete 2>/dev/null || true
        
        # 压缩镜像文件（如果存在）
        if [ -f "dist/nextchat-custom.tar" ]; then
            gzip -$COMPRESSION_LEVEL dist/nextchat-custom.tar
            log_info "镜像文件已压缩"
        fi
        
        # 显示优化后大小
        if [ "$SHOW_PROGRESS" = "true" ]; then
            local after_size=$(du -sh dist 2>/dev/null | cut -f1 || echo "未知")
            log_info "优化后大小: $after_size"
        fi
        
        log_success "dist 目录优化完成"
    fi
}

# 验证构建结果
validate_build() {
    log_info "验证构建结果..."
    
    local missing_files=()
    [ ! -f "dist/deploy.sh" ] && missing_files+=("deploy.sh")
    [ ! -d "dist/standalone" ] && missing_files+=("standalone目录")
    [ ! -f "dist/nextchat-custom.tar.gz" ] && [ ! -f "dist/nextchat-custom.tar" ] && missing_files+=("镜像文件")
    
    if [ ${#missing_files[@]} -eq 0 ]; then
        log_success "所有关键文件验证通过"
        if [ "$SHOW_PROGRESS" = "true" ]; then
            echo "  ✅ 部署脚本: dist/deploy.sh"
            echo "  ✅ 应用文件: dist/standalone/"
            [ -f "dist/nextchat-custom.tar.gz" ] && echo "  ✅ 镜像文件: dist/nextchat-custom.tar.gz"
            [ -f "dist/nextchat-custom.tar" ] && echo "  ✅ 镜像文件: dist/nextchat-custom.tar"
        fi
    else
        log_warning "缺少文件: ${missing_files[*]}"
    fi
}

# 显示帮助信息
show_help() {
    echo "NextChat 本地构建脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -h, --help        显示帮助信息"
    echo "  --no-optimize     跳过 dist 目录优化"
    echo "  --no-cleanup      跳过文件清理"
    echo "  --quiet           静默模式，减少输出"
    echo
    echo "示例:"
    echo "  $0                # 默认构建"
    echo "  $0 --no-optimize  # 跳过优化"
    echo "  $0 --quiet        # 静默构建"
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            --no-optimize)
                AUTO_OPTIMIZE=false
                shift
                ;;
            --no-cleanup)
                AUTO_CLEANUP=false
                shift
                ;;
            --quiet)
                SHOW_PROGRESS=false
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 主函数
main() {
    log_info "开始 NextChat 本地构建流程..."
    
    # 1. 环境检查
    log_info "1. 检查环境依赖..."
    check_command "node"
    check_command "yarn"
    check_command "docker"
    check_node_version
    log_success "环境检查完成"
    
    # 2. 检查 .env 文件
    log_info "2. 检查环境变量配置..."
    if [ ! -f ".env" ]; then
        if [ -f ".env.local" ]; then
            log_warning ".env 文件不存在，从 .env.local 复制"
            cp .env.local .env
        else
            log_error ".env 和 .env.local 文件都不存在"
            exit 1
        fi
    fi
    
    if grep -q "sk-your-deepseek-api-key-here" .env; then
        log_error "请在 .env 文件中设置正确的 DEEPSEEK_API_KEY"
        exit 1
    fi
    log_success "环境变量配置检查完成"
    
    # 3. 清理旧文件（但保留当前构建可能需要的文件）
    if [ "$AUTO_CLEANUP" = "true" ]; then
        log_info "清理旧文件..."
        # 只清理项目根目录的镜像文件，不清理dist中的
        rm -f nextchat-custom.tar nextchat-custom.tar.gz
        find . -name "*.tmp" -type f -delete 2>/dev/null || true
        find . -name ".DS_Store" -type f -delete 2>/dev/null || true
        log_success "旧文件清理完成"
    fi
    
    # 4. 安装依赖
    log_info "3. 安装项目依赖..."
    yarn install
    log_success "依赖安装完成"
    
    # 5. 本地构建
    log_info "4. 执行本地构建..."
    yarn build
    log_success "本地构建完成"
    
    # 6. 复制部署脚本和环境变量文件
    log_info "5. 复制部署脚本和环境变量文件到 dist 目录..."
    cp deploy.sh dist/
    chmod +x dist/deploy.sh
    
    # 复制环境变量文件到 dist 目录
    if [ -f ".env.local" ]; then
        cp .env.local dist/
        log_info "已复制 .env.local 到 dist 目录"
    elif [ -f ".env" ]; then
        cp .env dist/.env.local
        log_info "已复制 .env 到 dist/.env.local"
    fi
    log_success "部署脚本和环境变量文件已复制"
    
    # 7. 构建 Docker 镜像
    log_info "6. 构建 Docker 镜像..."
    DEEPSEEK_API_KEY=$(grep DEEPSEEK_API_KEY .env | cut -d '=' -f2)
    
    if [ -z "$DEEPSEEK_API_KEY" ]; then
        log_error "DEEPSEEK_API_KEY 未设置"
        exit 1
    fi
    
    # 获取代理配置（如果存在，忽略注释行）
    PROXY_URL=$(grep "^PROXY_URL=" .env 2>/dev/null | cut -d '=' -f2- || echo "")
    HTTP_PROXY=$(grep "^HTTP_PROXY=" .env 2>/dev/null | cut -d '=' -f2- || echo "")
    HTTPS_PROXY=$(grep "^HTTPS_PROXY=" .env 2>/dev/null | cut -d '=' -f2- || echo "")
    
    # 显示代理配置状态
    if [ -n "$PROXY_URL" ] || [ -n "$HTTP_PROXY" ] || [ -n "$HTTPS_PROXY" ]; then
        log_info "检测到代理配置:"
        [ -n "$PROXY_URL" ] && echo "  PROXY_URL: $PROXY_URL"
        [ -n "$HTTP_PROXY" ] && echo "  HTTP_PROXY: $HTTP_PROXY"
        [ -n "$HTTPS_PROXY" ] && echo "  HTTPS_PROXY: $HTTPS_PROXY"
    else
        log_info "未配置代理，如遇地区限制请在 .env 文件中添加代理配置"
    fi
    
    # 构建Docker镜像，包含代理支持
    docker build \
        --platform=linux/amd64 \
        --build-arg DEEPSEEK_API_KEY="$DEEPSEEK_API_KEY" \
        --build-arg PROXY_URL="$PROXY_URL" \
        --build-arg HTTP_PROXY="$HTTP_PROXY" \
        --build-arg HTTPS_PROXY="$HTTPS_PROXY" \
        -t nextchat-custom:latest \
        -f Dockerfile.custom .
    log_success "Docker 镜像构建完成（已包含代理配置支持）"
    
    # 8. 导出并压缩镜像
    log_info "7. 导出 Docker 镜像..."
    docker save -o nextchat-custom.tar nextchat-custom:latest
    gzip nextchat-custom.tar
    
    local file_size=$(du -h nextchat-custom.tar.gz | cut -f1)
    log_success "镜像导出完成: nextchat-custom.tar.gz ($file_size)"
    
    # 9. 复制镜像到 dist 目录
    log_info "8. 复制镜像文件到 dist 目录..."
    # 先清理dist中的旧镜像文件
    rm -f dist/nextchat-custom.tar dist/nextchat-custom.tar.gz
    cp nextchat-custom.tar.gz dist/
    log_success "镜像文件已复制到 dist 目录"
    
    # 10. 优化 dist 目录
    optimize_dist
    
    # 11. 验证构建结果
    validate_build
    
    # 12. 生成上传命令
    log_info "生成上传命令..."
    cat > upload_commands.txt << EOF
# 上传 dist 目录到服务器
scp -r dist/ username@server_ip:/path/to/

# 在服务器上部署
ssh username@server_ip
cd /path/to/dist
./deploy.sh -p 3000
EOF
    log_success "上传命令已生成到 upload_commands.txt"
    
    # 13. 完成总结
    local dist_size=$(du -sh dist/ | cut -f1)
    
    log_success "NextChat 构建完成！"
    echo
    log_info "📦 构建产物:"
    echo "  ✅ Docker 镜像: nextchat-custom:latest"
    echo "  ✅ 压缩文件: nextchat-custom.tar.gz ($file_size)"
    echo "  ✅ 优化后的 dist 目录: $dist_size"
    echo "  ✅ 部署脚本: dist/deploy.sh"
    echo "  ✅ 上传命令: upload_commands.txt"
    echo
    log_info "🚀 部署步骤:"
    echo "  1. 上传 dist 目录: scp -r dist/ username@server_ip:/path/to/"
    echo "  2. 服务器部署: cd /path/to/dist && ./deploy.sh -p 3000"
    echo
    log_info "💡 提示: 现在只需上传 $dist_size 的 dist 目录即可完成部署！"
    echo
}

# 解析参数并执行主函数
parse_args "$@"
main