# 简单代理配置指南

## 🎯 目标
解决 DeepSeek API 区域限制问题，通过代理访问 API。

## 🚀 快速配置

### 步骤 1: 设置 Nginx 代理

运行自动配置脚本：
```bash
chmod +x setup-nginx-proxy.sh
sudo ./setup-nginx-proxy.sh
```

这个脚本会：
- 安装 Nginx（如果未安装）
- 配置代理到 DeepSeek API
- 启动代理服务在端口 8080
- 配置防火墙规则

### 步骤 2: 配置环境变量

编辑 `.env.local` 文件：
```bash
nano .env.local
```

添加以下配置：
```bash
# 你的 DeepSeek API 密钥
DEEPSEEK_API_KEY=sk-your-deepseek-api-key-here

# 使用本地 Nginx 代理
PROXY_URL=http://localhost:8080
```

### 步骤 3: 重启应用

```bash
# 开发环境
npm run dev

# 或生产环境
pm2 restart nextchat
```

## 🧪 测试

1. **测试代理状态**：
   ```bash
   curl http://localhost:8080/health
   ```

2. **测试 API 连接**：
   ```bash
   curl -X POST http://localhost:8080/chat/completions \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer your-api-key" \
     -d '{"model":"deepseek-chat","messages":[{"role":"user","content":"test"}],"max_tokens":1}'
   ```

## 🔧 手动配置（可选）

如果自动脚本不工作，可以手动配置：

1. **安装 Nginx**：
   ```bash
   sudo apt install nginx  # Ubuntu/Debian
   sudo yum install nginx  # CentOS/RHEL
   ```

2. **创建代理配置**：
   ```bash
   sudo cp nginx-proxy.conf /etc/nginx/sites-available/deepseek-proxy
   sudo ln -s /etc/nginx/sites-available/deepseek-proxy /etc/nginx/sites-enabled/
   ```

3. **重启 Nginx**：
   ```bash
   sudo nginx -t
   sudo systemctl restart nginx
   ```

## 🔍 故障排除

### 常见问题

1. **端口被占用**：
   ```bash
   sudo netstat -tlnp | grep 8080
   sudo systemctl stop nginx
   sudo systemctl start nginx
   ```

2. **权限问题**：
   ```bash
   sudo chown -R nginx:nginx /var/log/nginx/
   sudo chmod 755 /var/log/nginx/
   ```

3. **防火墙阻止**：
   ```bash
   sudo ufw allow 8080/tcp
   # 或
   sudo firewall-cmd --permanent --add-port=8080/tcp
   sudo firewall-cmd --reload
   ```

### 检查日志

```bash
# Nginx 日志
sudo tail -f /var/log/nginx/deepseek_proxy_*.log

# 应用日志
pm2 logs nextchat

# 系统日志
sudo journalctl -u nginx -f
```

## 📋 配置说明

### 支持的代理格式

```bash
# HTTP 代理
PROXY_URL=http://localhost:8080

# HTTPS 代理
PROXY_URL=https://proxy.example.com:8080

# SOCKS5 代理
PROXY_URL=socks5://proxy.example.com:1080
```

### 环境变量优先级

1. `PROXY_URL` (推荐)
2. `HTTP_PROXY`
3. `HTTPS_PROXY`

## ✅ 验证成功

配置成功后，你应该看到：

1. **应用日志**显示：`[Proxy] Using proxy: http://localhost:8080`
2. **不再出现**区域限制错误
3. **聊天功能**正常工作

## 🚨 注意事项

1. **API 密钥安全**：不要在代码中硬编码 API 密钥
2. **代理安全**：只使用可信的代理服务器
3. **性能影响**：代理可能会增加一些延迟
4. **监控**：定期检查代理服务器状态

配置完成后，你的 NextChat 应用将通过 Nginx 代理访问 DeepSeek API，避免区域限制问题。
