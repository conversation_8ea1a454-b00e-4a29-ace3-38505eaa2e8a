#!/bin/bash

# DeepSeek API 配置测试脚本
# 用于诊断区域限制和认证问题

echo "=== DeepSeek API 配置诊断 ==="
echo

# 1. 检查环境变量文件
echo "1. 检查环境变量文件:"
if [ -f ".env.local" ]; then
    echo "✓ .env.local 文件存在"
    if grep -q "DEEPSEEK_API_KEY=" .env.local; then
        echo "✓ DEEPSEEK_API_KEY 配置存在"
        # 检查是否有语法错误（双等号）
        if grep -q "DEEPSEEK_API_KEY==" .env.local; then
            echo "❌ 发现语法错误：使用了双等号 (==)，应该使用单等号 (=)"
        else
            echo "✓ 语法正确：使用单等号"
        fi
        
        # 检查API密钥格式
        api_key=$(grep "DEEPSEEK_API_KEY=" .env.local | cut -d'=' -f2)
        if [[ $api_key == sk-* ]]; then
            echo "✓ API密钥格式正确 (以sk-开头)"
            echo "  密钥长度: ${#api_key} 字符"
        else
            echo "❌ API密钥格式错误，应该以 sk- 开头"
        fi
    else
        echo "❌ DEEPSEEK_API_KEY 配置不存在"
    fi
else
    echo "❌ .env.local 文件不存在"
fi

if [ -f "dist/.env.local" ]; then
    echo "✓ dist/.env.local 文件存在"
    if grep -q "DEEPSEEK_API_KEY=" dist/.env.local; then
        echo "✓ dist/.env.local 中 DEEPSEEK_API_KEY 配置存在"
        # 检查是否有语法错误
        if grep -q "DEEPSEEK_API_KEY==" dist/.env.local; then
            echo "❌ dist/.env.local 中发现语法错误：使用了双等号 (==)"
        else
            echo "✓ dist/.env.local 语法正确"
        fi
    fi
fi

echo

# 2. 检查代理配置
echo "2. 检查代理配置:"
if grep -q "^PROXY_URL=" .env.local 2>/dev/null; then
    proxy_url=$(grep "^PROXY_URL=" .env.local | cut -d'=' -f2)
    if [ -n "$proxy_url" ]; then
        echo "✓ 代理已配置: $proxy_url"
    else
        echo "⚠️  PROXY_URL 已定义但为空"
    fi
else
    echo "⚠️  未配置代理 (如遇区域限制请配置代理)"
fi

echo

# 3. 测试API连接
echo "3. 测试 DeepSeek API 连接:"
if [ -f ".env.local" ]; then
    source .env.local
    
    if [ -n "$DEEPSEEK_API_KEY" ]; then
        echo "正在测试API连接..."
        
        # 构建curl命令
        curl_cmd="curl -s -w '%{http_code}' -o /tmp/deepseek_test.json"
        
        # 如果配置了代理，添加代理参数
        if [ -n "$PROXY_URL" ]; then
            curl_cmd="$curl_cmd --proxy $PROXY_URL"
            echo "使用代理: $PROXY_URL"
        fi
        
        # 执行API测试
        http_code=$(eval "$curl_cmd -H 'Authorization: Bearer $DEEPSEEK_API_KEY' -H 'Content-Type: application/json' -d '{\"model\": \"deepseek-chat\", \"messages\": [{\"role\": \"user\", \"content\": \"test\"}], \"max_tokens\": 1}' https://api.deepseek.com/chat/completions")
        
        echo "HTTP状态码: $http_code"
        
        if [ -f "/tmp/deepseek_test.json" ]; then
            response=$(cat /tmp/deepseek_test.json)
            echo "API响应: $response"
            
            # 检查常见错误
            if echo "$response" | grep -q "unsupported_country_region_territory"; then
                echo "❌ 检测到区域限制错误！"
                echo "解决方案:"
                echo "  1. 配置代理: 在 .env.local 中设置 PROXY_URL=http://your-proxy:port"
                echo "  2. 使用VPN连接到支持的地区"
                echo "  3. 检查代理配置是否正确"
            elif echo "$response" | grep -q "invalid_api_key\|unauthorized"; then
                echo "❌ API密钥无效或认证失败！"
                echo "请检查:"
                echo "  1. API密钥是否正确"
                echo "  2. API密钥是否已激活"
                echo "  3. 账户是否有足够余额"
            elif [ "$http_code" = "200" ]; then
                echo "✓ API连接成功！"
            else
                echo "⚠️  API请求返回状态码: $http_code"
            fi
            
            rm -f /tmp/deepseek_test.json
        fi
    else
        echo "❌ DEEPSEEK_API_KEY 未设置"
    fi
else
    echo "❌ 无法加载环境变量文件"
fi

echo

# 4. 检查认证头格式
echo "4. 认证头格式检查:"
if [ -n "$DEEPSEEK_API_KEY" ]; then
    echo "API密钥: $DEEPSEEK_API_KEY"
    echo "认证头: Authorization: Bearer $DEEPSEEK_API_KEY"
    
    # 检查Bearer格式
    if [[ $DEEPSEEK_API_KEY == sk-* ]]; then
        echo "✓ 认证头格式正确"
    else
        echo "❌ API密钥格式错误，应该以 sk- 开头"
    fi
fi

echo
echo "=== 诊断完成 ==="
echo
echo "常见问题解决方案:"
echo "1. 区域限制: 配置 PROXY_URL 或使用VPN"
echo "2. API密钥错误: 检查密钥格式和有效性"
echo "3. 语法错误: 确保使用单等号 (=) 而不是双等号 (==)"
echo "4. 认证失败: 确保API密钥有效且账户有余额"