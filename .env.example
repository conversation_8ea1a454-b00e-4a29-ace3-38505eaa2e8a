# NextChat 环境变量配置示例
# 复制此文件为 .env 并填入实际值

# DeepSeek API 密钥 (必需)
DEEPSEEK_API_KEY=sk-your-deepseek-api-key-here

# 代理配置 (可选，用于解决地区限制)
# 如果遇到 'unsupported_country_region_territory' 错误，请配置以下代理设置
# 构建和部署脚本会自动读取这些配置

# 应用级代理URL (推荐)
# PROXY_URL=http://localhost:7890
# PROXY_URL=http://proxy.example.com:8080
# PROXY_URL=socks5://proxy.example.com:1080

# 系统级代理 (备选)
# HTTP_PROXY=http://localhost:7890
# HTTPS_PROXY=http://localhost:7890
# HTTP_PROXY=http://proxy.example.com:8080
# HTTPS_PROXY=http://proxy.example.com:8080

# 其他API配置 (可选)
# OPENAI_API_KEY=sk-your-openai-api-key
# ANTHROPIC_API_KEY=sk-ant-your-anthropic-key
# GOOGLE_API_KEY=your-google-api-key

# 自定义API端点 (可选)
# OPENAI_API_BASE_URL=https://api.openai.com/v1
# ANTHROPIC_API_BASE_URL=https://api.anthropic.com

# 应用配置
# NODE_ENV=production
# PORT=3000
# HOSTNAME=0.0.0.0

# 代理配置说明:
# 1. PROXY_URL: 应用级代理，支持 http/https/socks5 协议
# 2. HTTP_PROXY/HTTPS_PROXY: 系统级代理，影响所有网络请求
# 3. 优先级: 命令行参数 > 环境变量文件配置
# 4. 构建脚本 (build.sh) 和部署脚本 (deploy.sh) 会自动读取这些配置
# 5. 如果你的服务器在受限地区，建议配置代理来访问API服务
#
# 使用方法:
# 1. 复制此文件为 .env.local
# 2. 取消注释并设置相应的代理URL
# 3. 运行 ./build.sh 或 ./deploy.sh，脚本会自动应用代理配置
# 4. Docker容器启动时会自动传入代理环境变量