#!/bin/bash

# 简单的代理测试脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 测试代理健康状态
test_proxy_health() {
    log_info "测试代理健康状态..."
    
    if curl -s http://localhost:8080/health > /dev/null; then
        log_success "代理服务正常运行"
        return 0
    else
        log_error "代理服务不可用"
        return 1
    fi
}

# 测试环境变量配置
test_env_config() {
    log_info "检查环境变量配置..."
    
    if [ -f ".env.local" ]; then
        if grep -q "PROXY_URL=http://localhost:8080" .env.local; then
            log_success "PROXY_URL 配置正确"
        else
            log_warning "PROXY_URL 未配置或配置错误"
        fi
        
        if grep -q "DEEPSEEK_API_KEY=" .env.local; then
            api_key=$(grep "DEEPSEEK_API_KEY=" .env.local | cut -d'=' -f2)
            if [ "$api_key" != "your-deepseek-api-key-here" ] && [ -n "$api_key" ]; then
                log_success "DEEPSEEK_API_KEY 已配置"
                return 0
            else
                log_warning "DEEPSEEK_API_KEY 未设置或使用默认值"
                return 1
            fi
        else
            log_error "DEEPSEEK_API_KEY 未找到"
            return 1
        fi
    else
        log_error ".env.local 文件不存在"
        return 1
    fi
}

# 测试 DeepSeek API 连接
test_deepseek_api() {
    log_info "测试 DeepSeek API 连接..."
    
    if [ -f ".env.local" ]; then
        api_key=$(grep "DEEPSEEK_API_KEY=" .env.local | cut -d'=' -f2)
        
        if [ "$api_key" != "your-deepseek-api-key-here" ] && [ -n "$api_key" ]; then
            response=$(curl -s -w "HTTPSTATUS:%{http_code}" \
                -H "Content-Type: application/json" \
                -H "Authorization: Bearer $api_key" \
                -d '{"model":"deepseek-chat","messages":[{"role":"user","content":"Hello"}],"max_tokens":5}' \
                http://localhost:8080/chat/completions)
            
            status=$(echo "$response" | tr -d '\n' | sed -E 's/.*HTTPSTATUS:([0-9]{3})$/\1/')
            body=$(echo "$response" | sed -E 's/HTTPSTATUS\:[0-9]{3}$//')
            
            if [ "$status" = "200" ]; then
                log_success "DeepSeek API 连接成功"
                echo "响应: $body" | head -c 200
                echo ""
                return 0
            else
                log_error "DeepSeek API 连接失败，状态码: $status"
                echo "错误响应: $body"
                return 1
            fi
        else
            log_warning "跳过 API 测试，API 密钥未配置"
            return 1
        fi
    else
        log_error "无法读取 API 密钥"
        return 1
    fi
}

# 检查 Nginx 状态
check_nginx_status() {
    log_info "检查 Nginx 状态..."
    
    if systemctl is-active --quiet nginx; then
        log_success "Nginx 服务正在运行"
    else
        log_error "Nginx 服务未运行"
        log_info "尝试启动 Nginx..."
        if sudo systemctl start nginx; then
            log_success "Nginx 启动成功"
        else
            log_error "Nginx 启动失败"
            return 1
        fi
    fi
}

# 显示配置信息
show_config_info() {
    log_info "当前配置信息："
    echo ""
    echo "代理地址: http://localhost:8080"
    echo "健康检查: http://localhost:8080/health"
    echo ""
    
    if [ -f ".env.local" ]; then
        echo "环境变量配置:"
        grep -E "^(DEEPSEEK_API_KEY|PROXY_URL)" .env.local || echo "未找到相关配置"
    else
        echo "环境变量文件: 不存在"
    fi
    echo ""
}

# 主函数
main() {
    echo "========================================"
    echo "NextChat 代理配置测试"
    echo "========================================"
    echo ""
    
    show_config_info
    
    # 检查 Nginx 状态
    if ! check_nginx_status; then
        log_error "Nginx 检查失败，请先运行 setup-nginx-proxy.sh"
        exit 1
    fi
    
    # 测试代理健康状态
    if ! test_proxy_health; then
        log_error "代理健康检查失败"
        exit 1
    fi
    
    # 测试环境变量配置
    if test_env_config; then
        # 测试 API 连接
        if test_deepseek_api; then
            log_success "所有测试通过！代理配置正常工作"
        else
            log_warning "代理工作正常，但 API 连接测试失败"
            log_info "请检查 API 密钥是否正确"
        fi
    else
        log_warning "环境变量配置不完整"
        log_info "请编辑 .env.local 文件，设置正确的配置"
    fi
    
    echo ""
    log_info "如果遇到问题，请查看："
    echo "- Nginx 日志: sudo tail -f /var/log/nginx/deepseek_proxy_*.log"
    echo "- 应用日志: pm2 logs nextchat"
    echo "- 配置指南: cat SIMPLE_PROXY_GUIDE.md"
}

# 运行主函数
main "$@"
