#!/bin/bash

# NextChat 快速部署脚本
# 用于在服务器上快速部署 NextChat 应用

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 默认配置
DEFAULT_PORT=3000
CONTAINER_NAME="nextchat"
IMAGE_NAME="nextchat-optimized:latest"

# 显示帮助信息
show_help() {
    echo "NextChat 快速部署脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -k, --api-key KEY     设置 DeepSeek API 密钥（必需）"
    echo "  -p, --port PORT       设置服务端口（默认: 3000）"
    echo "  -x, --proxy URL       设置代理地址"
    echo "  -n, --name NAME       设置容器名称（默认: nextchat）"
    echo "  -h, --help            显示帮助信息"
    echo "  --stop                停止现有容器"
    echo "  --restart             重启容器"
    echo "  --logs                查看容器日志"
    echo "  --status              查看容器状态"
    echo
    echo "示例:"
    echo "  $0 -k sk-your-api-key                    # 基本部署"
    echo "  $0 -k sk-your-api-key -p 8080            # 指定端口"
    echo "  $0 -k sk-your-api-key -x http://proxy:7890  # 使用代理"
    echo "  $0 --stop                                # 停止服务"
    echo "  $0 --restart                             # 重启服务"
    echo
}

# 检查 Docker
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker 服务未运行，请启动 Docker 服务"
        exit 1
    fi
    
    log_info "Docker 检查通过"
}

# 加载镜像
load_image() {
    local image_file="nextchat-optimized.tar"
    local compressed_file="nextchat-optimized.tar.gz"
    
    # 检查镜像是否已存在
    if docker images | grep -q "nextchat-optimized"; then
        log_info "镜像已存在，跳过加载"
        return 0
    fi
    
    # 查找镜像文件
    if [ -f "$compressed_file" ]; then
        log_info "解压镜像文件..."
        gunzip "$compressed_file"
        image_file="nextchat-optimized.tar"
    fi
    
    if [ -f "$image_file" ]; then
        log_info "加载 Docker 镜像..."
        docker load -i "$image_file"
        log_success "镜像加载完成"
    else
        log_error "未找到镜像文件: $image_file 或 $compressed_file"
        exit 1
    fi
}

# 停止现有容器
stop_container() {
    if docker ps -q -f name="$CONTAINER_NAME" | grep -q .; then
        log_info "停止现有容器: $CONTAINER_NAME"
        docker stop "$CONTAINER_NAME"
        docker rm "$CONTAINER_NAME"
        log_success "容器已停止并删除"
    else
        log_info "容器 $CONTAINER_NAME 未运行"
    fi
}

# 启动容器
start_container() {
    local api_key="$1"
    local port="$2"
    local proxy_url="$3"
    
    if [ -z "$api_key" ]; then
        log_error "API 密钥不能为空"
        exit 1
    fi
    
    # 构建 docker run 命令
    local docker_cmd="docker run -d"
    docker_cmd="$docker_cmd -p $port:3000"
    docker_cmd="$docker_cmd -e DEEPSEEK_API_KEY='$api_key'"
    docker_cmd="$docker_cmd --name $CONTAINER_NAME"
    docker_cmd="$docker_cmd --restart unless-stopped"
    
    # 添加代理配置
    if [ -n "$proxy_url" ]; then
        docker_cmd="$docker_cmd -e PROXY_URL='$proxy_url'"
        log_info "使用代理: $proxy_url"
    fi
    
    docker_cmd="$docker_cmd $IMAGE_NAME"
    
    log_info "启动容器..."
    log_info "端口: $port"
    
    # 执行命令
    eval "$docker_cmd"
    
    # 等待容器启动
    sleep 3
    
    # 检查容器状态
    if docker ps | grep -q "$CONTAINER_NAME"; then
        log_success "容器启动成功！"
        log_info "访问地址: http://localhost:$port"
        log_info "容器名称: $CONTAINER_NAME"
    else
        log_error "容器启动失败，查看日志:"
        docker logs "$CONTAINER_NAME"
        exit 1
    fi
}

# 查看容器状态
show_status() {
    echo "=== 容器状态 ==="
    if docker ps | grep -q "$CONTAINER_NAME"; then
        docker ps | grep "$CONTAINER_NAME"
        echo
        echo "=== 资源使用 ==="
        docker stats --no-stream "$CONTAINER_NAME"
    else
        log_warning "容器 $CONTAINER_NAME 未运行"
    fi
}

# 查看日志
show_logs() {
    if docker ps -a | grep -q "$CONTAINER_NAME"; then
        log_info "显示容器日志（最近 50 行）:"
        docker logs --tail 50 "$CONTAINER_NAME"
    else
        log_error "容器 $CONTAINER_NAME 不存在"
    fi
}

# 重启容器
restart_container() {
    if docker ps -a | grep -q "$CONTAINER_NAME"; then
        log_info "重启容器: $CONTAINER_NAME"
        docker restart "$CONTAINER_NAME"
        log_success "容器重启完成"
    else
        log_error "容器 $CONTAINER_NAME 不存在"
    fi
}

# 解析命令行参数
API_KEY=""
PORT="$DEFAULT_PORT"
PROXY_URL=""
ACTION="deploy"

while [[ $# -gt 0 ]]; do
    case $1 in
        -k|--api-key)
            API_KEY="$2"
            shift 2
            ;;
        -p|--port)
            PORT="$2"
            shift 2
            ;;
        -x|--proxy)
            PROXY_URL="$2"
            shift 2
            ;;
        -n|--name)
            CONTAINER_NAME="$2"
            shift 2
            ;;
        --stop)
            ACTION="stop"
            shift
            ;;
        --restart)
            ACTION="restart"
            shift
            ;;
        --logs)
            ACTION="logs"
            shift
            ;;
        --status)
            ACTION="status"
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 主函数
main() {
    log_info "NextChat 快速部署脚本"
    
    check_docker
    
    case "$ACTION" in
        "deploy")
            if [ -z "$API_KEY" ]; then
                log_error "请提供 API 密钥，使用 -k 参数"
                show_help
                exit 1
            fi
            
            load_image
            stop_container
            start_container "$API_KEY" "$PORT" "$PROXY_URL"
            ;;
        "stop")
            stop_container
            ;;
        "restart")
            restart_container
            ;;
        "logs")
            show_logs
            ;;
        "status")
            show_status
            ;;
    esac
}

main