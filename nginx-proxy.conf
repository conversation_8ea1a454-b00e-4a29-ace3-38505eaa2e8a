# Nginx 代理配置示例
# 用于解决 DeepSeek API 区域限制问题

# 在 /etc/nginx/sites-available/ 创建此配置文件
# 然后创建软链接到 /etc/nginx/sites-enabled/

server {
    listen 8080;
    server_name localhost;
    
    # 日志配置
    access_log /var/log/nginx/deepseek_proxy_access.log;
    error_log /var/log/nginx/deepseek_proxy_error.log;
    
    # 代理 DeepSeek API
    location /v1/ {
        # 代理到 DeepSeek API
        proxy_pass https://api.deepseek.com/;
        
        # 保持原始请求头
        proxy_set_header Host api.deepseek.com;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 添加香港地区标识
        proxy_set_header CF-IPCountry HK;
        proxy_set_header CF-Connecting-IP *************;
        
        # SSL 配置
        proxy_ssl_verify off;
        proxy_ssl_server_name on;
        
        # 超时配置
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # 缓冲配置
        proxy_buffering off;
        proxy_request_buffering off;
        
        # 支持流式响应
        proxy_http_version 1.1;
        proxy_set_header Connection "";
    }
    
    # 健康检查端点
    location /health {
        return 200 "OK";
        add_header Content-Type text/plain;
    }
}

# 如果你想要 HTTPS 支持，可以添加以下配置：
# server {
#     listen 8443 ssl;
#     server_name localhost;
#     
#     # SSL 证书配置（需要自己生成或获取证书）
#     ssl_certificate /path/to/your/certificate.crt;
#     ssl_certificate_key /path/to/your/private.key;
#     
#     # SSL 配置
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers HIGH:!aNULL:!MD5;
#     
#     # 其他配置与上面相同
#     location /v1/ {
#         proxy_pass https://api.deepseek.com/;
#         # ... 其他代理配置
#     }
# }
