#!/bin/bash

# Nginx 代理测试脚本
# 用于验证 DeepSeek API 代理配置是否正常工作

set -e

echo "=== Nginx 代理测试脚本 ==="
echo "正在测试 Nginx 代理配置..."
echo

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试函数
test_result() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
        return 1
    fi
}

# 检查 Nginx 状态
echo "1. 检查 Nginx 服务状态..."
if systemctl is-active --quiet nginx; then
    test_result 0 "Nginx 服务正在运行"
else
    test_result 1 "Nginx 服务未运行"
    echo "请启动 Nginx: sudo systemctl start nginx"
    exit 1
fi

# 检查端口监听
echo "\n2. 检查端口 8080 监听状态..."
if netstat -tlnp 2>/dev/null | grep -q ":8080 " || ss -tlnp 2>/dev/null | grep -q ":8080 "; then
    test_result 0 "端口 8080 正在监听"
else
    test_result 1 "端口 8080 未监听"
    echo "请检查 Nginx 配置文件"
    exit 1
fi

# 测试健康检查端点
echo "\n3. 测试健康检查端点..."
HEALTH_RESPONSE=$(curl -s -w "%{http_code}" http://localhost:8080/health -o /tmp/health_response 2>/dev/null || echo "000")
if [ "$HEALTH_RESPONSE" = "200" ]; then
    test_result 0 "健康检查端点正常 (HTTP $HEALTH_RESPONSE)"
else
    test_result 1 "健康检查端点异常 (HTTP $HEALTH_RESPONSE)"
    echo "请检查 Nginx 配置和日志"
fi

# 检查 DeepSeek API 密钥
echo "\n4. 检查 DeepSeek API 密钥配置..."
if [ -f "dist/.env.local" ]; then
    if grep -q "DEEPSEEK_API_KEY=sk-" dist/.env.local; then
        API_KEY=$(grep "DEEPSEEK_API_KEY=" dist/.env.local | cut -d'=' -f2 | tr -d '"\047')
        if [[ $API_KEY =~ ^sk-[a-zA-Z0-9]{32}$ ]]; then
            test_result 0 "DeepSeek API 密钥格式正确"
        else
            test_result 1 "DeepSeek API 密钥格式错误"
            echo "密钥应该以 sk- 开头，后跟32位字符"
        fi
    else
        test_result 1 "未找到 DeepSeek API 密钥"
        echo "请在 dist/.env.local 中配置 DEEPSEEK_API_KEY"
    fi
else
    echo -e "${YELLOW}⚠️  未找到 dist/.env.local 文件${NC}"
fi

# 测试代理连接（如果有 API 密钥）
if [ ! -z "$API_KEY" ] && [[ $API_KEY =~ ^sk-[a-zA-Z0-9]{32}$ ]]; then
    echo "\n5. 测试 DeepSeek API 代理连接..."
    
    # 创建测试请求
    TEST_REQUEST='{
        "model": "deepseek-chat",
        "messages": [
            {"role": "user", "content": "Hello"}
        ],
        "max_tokens": 10,
        "stream": false
    }'
    
    # 发送测试请求
    RESPONSE=$(curl -s -w "\n%{http_code}" \
        -X POST http://localhost:8080/v1/chat/completions \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $API_KEY" \
        -d "$TEST_REQUEST" 2>/dev/null)
    
    HTTP_CODE=$(echo "$RESPONSE" | tail -n1)
    RESPONSE_BODY=$(echo "$RESPONSE" | head -n -1)
    
    if [ "$HTTP_CODE" = "200" ]; then
        test_result 0 "DeepSeek API 代理连接成功 (HTTP $HTTP_CODE)"
        echo "响应预览: $(echo "$RESPONSE_BODY" | jq -r '.choices[0].message.content // .error.message // "API 响应正常"' 2>/dev/null || echo "API 响应正常")"
    elif [ "$HTTP_CODE" = "401" ]; then
        test_result 1 "API 密钥无效 (HTTP $HTTP_CODE)"
        echo "请检查 DeepSeek API 密钥是否正确"
    elif [ "$HTTP_CODE" = "403" ] || [ "$HTTP_CODE" = "451" ]; then
        test_result 1 "区域限制错误 (HTTP $HTTP_CODE)"
        echo "需要配置代理服务器或使用其他网络环境"
    else
        test_result 1 "API 请求失败 (HTTP $HTTP_CODE)"
        echo "响应: $RESPONSE_BODY"
    fi
else
    echo "\n5. 跳过 API 连接测试（无有效 API 密钥）"
fi

# 检查 Nginx 日志
echo "\n6. 检查 Nginx 日志..."
if [ -f "/var/log/nginx/deepseek_error.log" ]; then
    ERROR_COUNT=$(wc -l < /var/log/nginx/deepseek_error.log 2>/dev/null || echo "0")
    if [ "$ERROR_COUNT" -eq 0 ]; then
        test_result 0 "无 Nginx 错误日志"
    else
        echo -e "${YELLOW}⚠️  发现 $ERROR_COUNT 条错误日志${NC}"
        echo "最近的错误:"
        tail -n 3 /var/log/nginx/deepseek_error.log 2>/dev/null || echo "无法读取日志"
    fi
else
    echo -e "${YELLOW}⚠️  未找到 Nginx 错误日志文件${NC}"
fi

# 显示配置建议
echo "\n=== 配置建议 ==="
echo "1. NextChat 环境变量配置:"
echo "   DEEPSEEK_URL=http://localhost:8080/v1"
echo "   # Docker 环境使用: http://host.docker.internal:8080/v1"
echo
echo "2. 常用管理命令:"
echo "   查看 Nginx 状态: sudo systemctl status nginx"
echo "   重启 Nginx: sudo systemctl restart nginx"
echo "   查看访问日志: sudo tail -f /var/log/nginx/deepseek_access.log"
echo "   查看错误日志: sudo tail -f /var/log/nginx/deepseek_error.log"
echo
echo "3. 故障排除:"
echo "   - 检查防火墙: sudo firewall-cmd --list-ports 或 sudo ufw status"
echo "   - 检查 SELinux: getenforce (如果是 Enforcing，可能需要配置策略)"
echo "   - 测试网络: curl -I https://api.deepseek.com"
echo
echo "测试完成！"