# NextChat Nginx 代理快速部署指南

本指南专为阿里云等云服务器环境设计，使用 Nginx 作为代理解决 DeepSeek API 区域限制问题。

## 🚀 一键部署

### 方案1：自动配置 Nginx 代理

```bash
# 1. 配置 Nginx 代理 (需要 root 权限)
sudo ./deploy.sh --setup-nginx-proxy

# 2. 部署 NextChat
./deploy.sh
```

### 方案2：手动配置 Nginx 代理

```bash
# 1. 手动运行 Nginx 配置脚本
sudo ./setup-nginx-proxy.sh

# 2. 测试代理配置
./test-nginx-proxy.sh

# 3. 部署 NextChat
./deploy.sh
```

## 📋 详细步骤

### 步骤1：配置 Nginx 代理

在阿里云服务器上运行：

```bash
# 使用集成的部署脚本
sudo ./deploy.sh --setup-nginx-proxy
```

这个命令会自动：
- 安装 Nginx
- 创建代理配置文件
- 配置防火墙规则
- 启动 Nginx 服务
- 测试代理连接

### 步骤2：配置环境变量

编辑 `dist/.env.local` 文件：

```bash
# DeepSeek API 配置
DEEPSEEK_API_KEY=sk-your-api-key-here
DEEPSEEK_URL=http://localhost:8080/v1

# 其他配置保持不变
ACCESS_CODE=your-access-code
```

**重要说明：**
- 如果 NextChat 运行在 Docker 中，使用：`DEEPSEEK_URL=http://host.docker.internal:8080/v1`
- 确保 API 密钥格式正确（以 `sk-` 开头）

### 步骤3：部署 NextChat

```bash
# 标准部署
./deploy.sh

# 或指定端口
./deploy.sh -p 3000
```

### 步骤4：验证部署

```bash
# 测试 Nginx 代理
./deploy.sh --test-nginx-proxy

# 检查服务状态
curl http://localhost:3000
curl http://localhost:8080/health
```

## 🔧 配置文件说明

### Nginx 配置文件位置

- 配置文件：`/etc/nginx/conf.d/deepseek-proxy.conf`
- 访问日志：`/var/log/nginx/deepseek_access.log`
- 错误日志：`/var/log/nginx/deepseek_error.log`

### 端口配置

- **NextChat 服务**：3000 (默认)
- **Nginx 代理**：8080
- **健康检查**：http://localhost:8080/health

## 🛠️ 管理命令

### Nginx 服务管理

```bash
# 查看状态
sudo systemctl status nginx

# 重启服务
sudo systemctl restart nginx

# 重新加载配置
sudo systemctl reload nginx

# 测试配置
sudo nginx -t
```

### 日志查看

```bash
# 查看访问日志
sudo tail -f /var/log/nginx/deepseek_access.log

# 查看错误日志
sudo tail -f /var/log/nginx/deepseek_error.log

# 查看 NextChat 容器日志
docker logs -f nextchat-app
```

### 防火墙管理

```bash
# CentOS/RHEL (firewalld)
sudo firewall-cmd --list-ports
sudo firewall-cmd --permanent --add-port=8080/tcp
sudo firewall-cmd --reload

# Ubuntu (UFW)
sudo ufw status
sudo ufw allow 8080/tcp
```

## 🔍 故障排除

### 常见问题

#### 1. Nginx 代理无法访问

```bash
# 检查 Nginx 状态
sudo systemctl status nginx

# 检查端口监听
sudo netstat -tlnp | grep :8080

# 检查防火墙
sudo firewall-cmd --list-ports  # CentOS
sudo ufw status                 # Ubuntu
```

#### 2. API 请求失败

```bash
# 测试代理连接
curl -I http://localhost:8080/health

# 检查环境变量
grep DEEPSEEK dist/.env.local

# 运行诊断脚本
./test-nginx-proxy.sh
```

#### 3. Docker 容器无法访问代理

确保使用正确的代理地址：

```bash
# 在 dist/.env.local 中
DEEPSEEK_URL=http://host.docker.internal:8080/v1
```

#### 4. SELinux 问题 (CentOS/RHEL)

```bash
# 检查 SELinux 状态
getenforce

# 临时禁用 (仅用于测试)
sudo setenforce 0

# 永久配置 SELinux 策略 (推荐)
sudo setsebool -P httpd_can_network_connect 1
```

### 诊断命令

```bash
# 完整诊断
./test-nginx-proxy.sh

# 手动测试 API
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-your-api-key" \
  -d '{
    "model": "deepseek-chat",
    "messages": [{"role": "user", "content": "Hello"}],
    "max_tokens": 10
  }'
```

## 📊 性能优化

### Nginx 优化配置

编辑 `/etc/nginx/nginx.conf`：

```nginx
worker_processes auto;
worker_connections 1024;

http {
    # 启用 gzip 压缩
    gzip on;
    gzip_types text/plain application/json;
    
    # 连接池优化
    upstream deepseek_backend {
        server api.deepseek.com:443;
        keepalive 32;
    }
}
```

### 监控配置

```bash
# 添加监控脚本到 crontab
echo "*/5 * * * * curl -s http://localhost:8080/health > /dev/null || systemctl restart nginx" | sudo crontab -
```

## 🔒 安全建议

1. **限制访问**：配置防火墙只允许必要的端口
2. **日志轮转**：配置 logrotate 管理日志文件
3. **SSL 证书**：为生产环境配置 HTTPS
4. **访问控制**：使用 Nginx 的 access 模块限制访问

## 📝 总结

使用 Nginx 代理的优势：

- ✅ **轻量级**：比 Clash 等代理软件更轻量
- ✅ **高性能**：优秀的并发处理能力
- ✅ **易管理**：标准的系统服务管理
- ✅ **灵活配置**：可精确控制代理规则
- ✅ **日志完整**：便于监控和调试
- ✅ **云服务器友好**：特别适合阿里云等环境

这种方案特别适合不想安装额外代理软件的云服务器环境，通过 Nginx 的反向代理功能即可有效解决 DeepSeek API 的区域限制问题。