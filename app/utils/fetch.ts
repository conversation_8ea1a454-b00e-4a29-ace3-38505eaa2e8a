import { getServerSideConfig } from "@/app/config/server";

// 检查是否在Node.js环境中（更严格的检测）
function isNodeEnvironment(): boolean {
  return (
    typeof process !== "undefined" &&
    process.versions &&
    !!process.versions.node &&
    typeof require !== "undefined" &&
    !process.env.NEXT_RUNTIME
  );
}

// 动态导入代理模块（仅在Node.js环境中）
async function createProxyAgent(proxyUrl: string, targetUrl: string) {
  // 双重检查：确保在Node.js环境且不是Edge Runtime
  if (!isNodeEnvironment() || process.env.NEXT_RUNTIME === "edge") {
    return null;
  }

  try {
    // 使用eval来避免webpack静态分析
    const httpsProxyAgent = await eval('import("https-proxy-agent")');
    const httpProxyAgent = await eval('import("http-proxy-agent")');
    const socksProxyAgent = await eval('import("socks-proxy-agent")');

    const { HttpsProxyAgent } = httpsProxyAgent;
    const { HttpProxyAgent } = httpProxyAgent;
    const { SocksProxyAgent } = socksProxyAgent;

    const url = new URL(targetUrl);

    if (proxyUrl.startsWith("socks")) {
      return new SocksProxyAgent(proxyUrl);
    } else if (url.protocol === "https:") {
      return new HttpsProxyAgent(proxyUrl);
    } else {
      return new HttpProxyAgent(proxyUrl);
    }
  } catch (error) {
    console.warn("[Proxy] Failed to load proxy modules:", error);
    return null;
  }
}

// 创建支持代理的fetch函数
export async function proxyFetch(
  url: string,
  options: RequestInit = {},
): Promise<Response> {
  const fetchOptions = { ...options };

  // 获取代理配置，优先使用PROXY_URL
  const serverConfig = getServerSideConfig();
  const proxyUrl = serverConfig.proxyUrl ||
                   process.env.HTTP_PROXY ||
                   process.env.HTTPS_PROXY;

  if (proxyUrl && isNodeEnvironment()) {
    console.log("[Proxy] Using proxy:", proxyUrl);

    try {
      const agent = await createProxyAgent(proxyUrl, url);
      if (agent) {
        // @ts-ignore
        fetchOptions.agent = agent;
      }
    } catch (error) {
      console.warn("[Proxy] Failed to setup proxy agent:", error);
    }
  }

  return fetch(url, fetchOptions);
}

// 导出默认的fetch函数，优先使用代理
export { proxyFetch as fetch };
export default proxyFetch;
