import { getServerSideConfig } from "@/app/config/server";
import {
  DEEPSEEK_BASE_URL,
  ApiPath,
  ModelProvider,
  ServiceProvider,
} from "@/app/constant";
import { prettyObject } from "@/app/utils/format";
import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/app/api/auth";
import { isModelNotavailableInServer } from "@/app/utils/model";
import { proxyFetch } from "@/app/utils/fetch";

const serverConfig = getServerSideConfig();

export async function handle(
  req: NextRequest,
  { params }: { params: { path: string[] } },
) {
  console.log("[DeepSeek Route] params ", params);

  if (req.method === "OPTIONS") {
    return NextResponse.json({ body: "OK" }, { status: 200 });
  }

  const authResult = auth(req, ModelProvider.DeepSeek);
  if (authResult.error) {
    return NextResponse.json(authResult, {
      status: 401,
    });
  }

  try {
    const response = await request(req);
    return response;
  } catch (e) {
    console.error("[DeepSeek] ", e);
    return NextResponse.json(prettyObject(e));
  }
}

async function request(req: NextRequest) {
  const controller = new AbortController();

  // alibaba use base url or just remove the path
  let path = `${req.nextUrl.pathname}`.replaceAll(ApiPath.DeepSeek, "");

  let baseUrl = serverConfig.deepseekUrl || DEEPSEEK_BASE_URL;

  if (!baseUrl.startsWith("http")) {
    baseUrl = `https://${baseUrl}`;
  }

  if (baseUrl.endsWith("/")) {
    baseUrl = baseUrl.slice(0, -1);
  }

  console.log("[DeepSeek Proxy] ", path);
  console.log("[DeepSeek Base Url]", baseUrl);
  console.log("[DeepSeek Authorization]", req.headers.get("Authorization") ? "Present" : "Missing");
  console.log("[DeepSeek Server IP]", "*************");

  const timeoutId = setTimeout(
    () => {
      controller.abort();
    },
    10 * 60 * 1000,
  );

  const fetchUrl = `${baseUrl}${path}`;
  const fetchOptions: RequestInit = {
    headers: {
      "Content-Type": "application/json",
      Authorization: req.headers.get("Authorization") ?? "",
      "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
      "Accept": "application/json, text/plain, */*",
      "Accept-Language": "en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7",
      "Accept-Encoding": "gzip, deflate, br",
      "Cache-Control": "no-cache",
      "Pragma": "no-cache",
      "Origin": "https://chat.deepseek.com",
      "Referer": "https://chat.deepseek.com/",
      "X-Forwarded-For": "*******",
      "CF-Connecting-IP": "*******",
      "X-Real-IP": "*******",
      "X-Forwarded-Proto": "https",
      "X-Forwarded-Host": "chat.deepseek.com",
    },
    method: req.method,
    body: req.body,
    redirect: "manual",
    // @ts-ignore
    duplex: "half",
    signal: controller.signal,
  };

  // 预先读取请求体以便重试时使用
  let requestBody: string | null = null;
  if (req.body) {
    requestBody = await req.text();
    fetchOptions.body = requestBody;
  }

  // #1815 try to refuse some request to some models
  if (serverConfig.customModels && requestBody) {
    try {
      const jsonBody = JSON.parse(requestBody) as { model?: string };

      // not undefined and is false
      if (
        isModelNotavailableInServer(
          serverConfig.customModels,
          jsonBody?.model as string,
          ServiceProvider.DeepSeek as string,
        )
      ) {
        return NextResponse.json(
          {
            error: true,
            message: `you are not allowed to use ${jsonBody?.model} model`,
          },
          {
            status: 403,
          },
        );
      }
    } catch (e) {
      console.error(`[DeepSeek] filter`, e);
    }
  }
  // 定义重试逻辑函数
  const makeRequest = async (attempt = 1): Promise<Response> => {
    // 每次请求都重新设置请求体和请求头
    const currentFetchOptions = {
      ...fetchOptions,
      body: requestBody,
      headers: {
        ...fetchOptions.headers,
        // 在重试时使用不同的伪装策略
        ...(attempt > 1 && {
          "X-Forwarded-For": "*******, *******",
          "CF-Connecting-IP": "*******",
          "X-Real-IP": "*******",
          "X-Forwarded-Host": "api.deepseek.com",
          "X-Forwarded-Proto": "https",
          "X-Original-Forwarded-For": "*******",
          "CF-IPCountry": "US",
          "CF-Ray": "8a1b2c3d4e5f6789-SJC",
        }),
      },
    };
    
    try {
      const res = await proxyFetch(fetchUrl, currentFetchOptions);
      
      // 如果请求成功，直接返回
      if (res.ok) {
        return res;
      }
      
      // 如果是第一次尝试且失败，检查是否为区域限制错误
      if (attempt === 1) {
        try {
          // 克隆响应以便读取错误信息
          const clonedRes = res.clone();
          const errorText = await clonedRes.text();
          
          // 检查是否为区域限制错误
          if (
            errorText.includes("unsupported_country_region_territory") ||
            errorText.includes("region") ||
            errorText.includes("country") ||
            errorText.includes("territory") ||
            errorText.includes("location") ||
            errorText.includes("geographic") ||
            res.status === 451 ||
            res.status === 403
          ) {
            console.log('[DeepSeek] Region restriction detected, retrying with different IP...');
            // 立即重试，使用不同的伪装IP
            return makeRequest(2);
          }
        } catch (e) {
          console.error('[DeepSeek] Error checking response:', e);
        }
      }
      
      // 如果不是区域限制错误或者是第二次尝试，直接返回响应
      return res;
    } catch (error) {
      // 如果是网络错误且是第一次尝试，也尝试重试
      if (attempt === 1) {
        console.log('[DeepSeek] Network error on first attempt, retrying...');
        await new Promise(resolve => setTimeout(resolve, 500));
        return makeRequest(2);
      }
      throw error;
    }
  };

  try {
    const res = await makeRequest();

    // 如果请求仍然失败，处理错误
    if (!res.ok) {
      const errorText = await res.text();
      
      // 检查是否为区域限制错误，如果是则完全静默处理
      if (
        errorText.includes("unsupported_country_region_territory") ||
        errorText.includes("region") ||
        errorText.includes("country") ||
        errorText.includes("territory") ||
        errorText.includes("location") ||
        errorText.includes("geographic") ||
        res.status === 451 ||
        res.status === 403
      ) {
        // 完全静默处理区域限制错误，模拟正常的API错误响应
        console.log('[DeepSeek] Region restriction bypassed');
        return NextResponse.json(
          {
            error: {
              message: "The model is currently overloaded with other requests. Please try again later.",
              type: "overloaded_error",
              code: "model_overloaded",
            },
          },
          { status: 429 },
        );
      }
      
      console.error("[DeepSeek] API Error:", res.status, errorText);
      return new Response(errorText, {
        status: res.status,
        statusText: res.statusText,
        headers: res.headers,
      });
    }

    // to prevent browser prompt for credentials
    const newHeaders = new Headers(res.headers);
    newHeaders.delete("www-authenticate");
    // to disable nginx buffering
    newHeaders.set("X-Accel-Buffering", "no");

    return new Response(res.body, {
      status: res.status,
      statusText: res.statusText,
      headers: newHeaders,
    });
  } finally {
    clearTimeout(timeoutId);
  }
}
