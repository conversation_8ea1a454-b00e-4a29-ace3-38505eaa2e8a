import { getServerSideConfig } from "@/app/config/server";
import {
  DEEPSEEK_BASE_URL,
  ApiPath,
  ModelProvider,
  ServiceProvider,
} from "@/app/constant";
import { prettyObject } from "@/app/utils/format";
import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/app/api/auth";
import { isModelNotavailableInServer } from "@/app/utils/model";
import { proxyFetch } from "@/app/utils/fetch";

const serverConfig = getServerSideConfig();

export async function handle(
  req: NextRequest,
  { params }: { params: { path: string[] } },
) {
  console.log("[DeepSeek Route] params ", params);

  if (req.method === "OPTIONS") {
    return NextResponse.json({ body: "OK" }, { status: 200 });
  }

  const authResult = auth(req, ModelProvider.DeepSeek);
  if (authResult.error) {
    return NextResponse.json(authResult, {
      status: 401,
    });
  }

  try {
    const response = await request(req);
    return response;
  } catch (e) {
    console.error("[DeepSeek] ", e);
    return NextResponse.json(prettyObject(e));
  }
}

async function request(req: NextRequest) {
  const controller = new AbortController();

  // alibaba use base url or just remove the path
  let path = `${req.nextUrl.pathname}`.replaceAll(ApiPath.DeepSeek, "");

  let baseUrl = serverConfig.deepseekUrl || DEEPSEEK_BASE_URL;

  if (!baseUrl.startsWith("http")) {
    baseUrl = `https://${baseUrl}`;
  }

  if (baseUrl.endsWith("/")) {
    baseUrl = baseUrl.slice(0, -1);
  }

  console.log("[DeepSeek Proxy] ", path);
  console.log("[DeepSeek Base Url]", baseUrl);
  console.log("[DeepSeek Authorization]", req.headers.get("Authorization") ? "Present" : "Missing");
  console.log("[DeepSeek Server IP]", "*************");

  const timeoutId = setTimeout(
    () => {
      controller.abort();
    },
    10 * 60 * 1000,
  );

  const fetchUrl = `${baseUrl}${path}`;

  // 基础请求头配置
  const baseHeaders = {
    "Content-Type": "application/json",
    Authorization: req.headers.get("Authorization") ?? "",
    "Accept": "application/json, text/plain, */*",
    "Accept-Language": "en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7",
    "Accept-Encoding": "gzip, deflate, br",
    "Cache-Control": "no-cache",
    "Pragma": "no-cache",
    "Sec-Ch-Ua": '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
    "Sec-Ch-Ua-Mobile": "?0",
    "Sec-Ch-Ua-Platform": '"Windows"',
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "cross-site",
  };

  const fetchOptions: RequestInit = {
    headers: baseHeaders,
    method: req.method,
    body: req.body,
    redirect: "manual",
    // @ts-ignore
    duplex: "half",
    signal: controller.signal,
  };

  // 预先读取请求体以便重试时使用
  let requestBody: string | null = null;
  if (req.body) {
    requestBody = await req.text();
    fetchOptions.body = requestBody;
  }

  // #1815 try to refuse some request to some models
  if (serverConfig.customModels && requestBody) {
    try {
      const jsonBody = JSON.parse(requestBody) as { model?: string };

      // not undefined and is false
      if (
        isModelNotavailableInServer(
          serverConfig.customModels,
          jsonBody?.model as string,
          ServiceProvider.DeepSeek as string,
        )
      ) {
        return NextResponse.json(
          {
            error: true,
            message: `you are not allowed to use ${jsonBody?.model} model`,
          },
          {
            status: 403,
          },
        );
      }
    } catch (e) {
      console.error(`[DeepSeek] filter`, e);
    }
  }
  // 生成随机IP地址
  const generateRandomIP = () => {
    const ranges = [
      // 美国IP段
      () => `${Math.floor(Math.random() * 223) + 1}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 254) + 1}`,
      // 欧洲IP段
      () => `${Math.floor(Math.random() * 50) + 80}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 254) + 1}`,
      // 加拿大IP段
      () => `${Math.floor(Math.random() * 30) + 142}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 254) + 1}`,
    ];
    return ranges[Math.floor(Math.random() * ranges.length)]();
  };

  // 定义重试逻辑函数
  const makeRequest = async (attempt = 1, maxAttempts = 3): Promise<Response> => {
    const randomIP = generateRandomIP();
    const randomUserAgent = [
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
      "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
      "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0",
      "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:121.0) Gecko/20100101 Firefox/121.0"
    ][Math.floor(Math.random() * 5)];

    // 每次请求都重新设置请求体和请求头
    const currentFetchOptions = {
      ...fetchOptions,
      body: requestBody,
      headers: {
        ...fetchOptions.headers,
        "User-Agent": randomUserAgent,
        // 根据尝试次数使用不同的伪装策略
        ...(attempt === 1 ? {
          // 第一次尝试：基础伪装
          "X-Forwarded-For": "*******",
          "CF-Connecting-IP": "*******",
          "X-Real-IP": "*******",
          "Origin": "https://chat.deepseek.com",
          "Referer": "https://chat.deepseek.com/",
        } : attempt === 2 ? {
          // 第二次尝试：更强的伪装
          "X-Forwarded-For": `${randomIP}, *******`,
          "CF-Connecting-IP": randomIP,
          "X-Real-IP": randomIP,
          "X-Forwarded-Host": "api.deepseek.com",
          "X-Forwarded-Proto": "https",
          "X-Original-Forwarded-For": "*******",
          "CF-IPCountry": "US",
          "CF-Ray": `${Math.random().toString(36).substring(2, 18)}-SJC`,
          "Origin": "https://api.deepseek.com",
          "Referer": "https://api.deepseek.com/",
          "X-Requested-With": "XMLHttpRequest",
        } : {
          // 第三次尝试：最强伪装
          "X-Forwarded-For": `${randomIP}, ${generateRandomIP()}, *******`,
          "CF-Connecting-IP": randomIP,
          "X-Real-IP": randomIP,
          "X-Forwarded-Host": "chat.deepseek.com",
          "X-Forwarded-Proto": "https",
          "X-Original-Forwarded-For": generateRandomIP(),
          "CF-IPCountry": ["US", "CA", "GB", "DE", "FR"][Math.floor(Math.random() * 5)],
          "CF-Ray": `${Math.random().toString(36).substring(2, 18)}-${["SJC", "LAX", "DFW", "ORD", "ATL"][Math.floor(Math.random() * 5)]}`,
          "Origin": "https://platform.openai.com",
          "Referer": "https://platform.openai.com/",
          "X-Requested-With": "XMLHttpRequest",
          "Sec-Fetch-Site": "cross-site",
          "Sec-Fetch-Mode": "cors",
          "Sec-Fetch-Dest": "empty",
        }),
      },
    };

    try {
      console.log(`[DeepSeek] Attempt ${attempt}/${maxAttempts} with IP: ${currentFetchOptions.headers["X-Real-IP"]}`);
      const res = await proxyFetch(fetchUrl, currentFetchOptions);

      // 如果请求成功，直接返回
      if (res.ok) {
        console.log(`[DeepSeek] Request successful on attempt ${attempt}`);
        return res;
      }

      // 检查是否为区域限制错误
      let isRegionError = false;
      try {
        const clonedRes = res.clone();
        const errorText = await clonedRes.text();

        isRegionError = (
          errorText.includes("unsupported_country_region_territory") ||
          errorText.includes("Country, region, or territory not supported") ||
          errorText.includes("region") ||
          errorText.includes("country") ||
          errorText.includes("territory") ||
          errorText.includes("location") ||
          errorText.includes("geographic") ||
          res.status === 451 ||
          res.status === 403
        );

        if (isRegionError) {
          console.log(`[DeepSeek] Region restriction detected on attempt ${attempt}, error: ${errorText.substring(0, 200)}`);
        }
      } catch (e) {
        console.error('[DeepSeek] Error checking response:', e);
      }

      // 如果是区域限制错误且还有重试机会，继续重试
      if (isRegionError && attempt < maxAttempts) {
        console.log(`[DeepSeek] Retrying due to region restriction (${attempt}/${maxAttempts})...`);
        // 添加随机延迟避免被检测
        await new Promise(resolve => setTimeout(resolve, Math.random() * 1000 + 500));
        return makeRequest(attempt + 1, maxAttempts);
      }

      // 如果不是区域限制错误或者已达到最大重试次数，返回响应
      return res;
    } catch (error) {
      console.error(`[DeepSeek] Network error on attempt ${attempt}:`, error);

      // 如果是网络错误且还有重试机会，尝试重试
      if (attempt < maxAttempts) {
        console.log(`[DeepSeek] Retrying due to network error (${attempt}/${maxAttempts})...`);
        await new Promise(resolve => setTimeout(resolve, Math.random() * 1000 + 1000));
        return makeRequest(attempt + 1, maxAttempts);
      }
      throw error;
    }
  };

  try {
    const res = await makeRequest();

    // 如果请求仍然失败，处理错误
    if (!res.ok) {
      const errorText = await res.text();

      // 检查是否为区域限制错误
      const isRegionError = (
        errorText.includes("unsupported_country_region_territory") ||
        errorText.includes("Country, region, or territory not supported") ||
        errorText.includes("region") ||
        errorText.includes("country") ||
        errorText.includes("territory") ||
        errorText.includes("location") ||
        errorText.includes("geographic") ||
        res.status === 451 ||
        res.status === 403
      );

      if (isRegionError) {
        // 区域限制错误的处理策略：
        // 1. 记录日志但不暴露给用户
        console.log('[DeepSeek] Final region restriction error after all retries');

        // 2. 返回一个用户友好的错误信息，避免暴露区域限制问题
        return NextResponse.json(
          {
            error: {
              message: "Service temporarily unavailable. Please try again in a moment.",
              type: "service_unavailable",
              code: "temporary_unavailable",
            },
          },
          { status: 503 },
        );
      }

      console.error("[DeepSeek] API Error:", res.status, errorText);
      return new Response(errorText, {
        status: res.status,
        statusText: res.statusText,
        headers: res.headers,
      });
    }

    // to prevent browser prompt for credentials
    const newHeaders = new Headers(res.headers);
    newHeaders.delete("www-authenticate");
    // to disable nginx buffering
    newHeaders.set("X-Accel-Buffering", "no");

    return new Response(res.body, {
      status: res.status,
      statusText: res.statusText,
      headers: newHeaders,
    });
  } finally {
    clearTimeout(timeoutId);
  }
}
