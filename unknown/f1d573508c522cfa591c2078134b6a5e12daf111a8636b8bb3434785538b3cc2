FROM node:18-alpine AS base

# Production image, copy pre-built files
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

# Install proxychains for proxy support - DISABLED to avoid errors
# RUN apk add --no-cache proxychains-ng

# Create user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copy pre-built application
COPY --chown=nextjs:nodejs ./dist/standalone ./
COPY --chown=nextjs:nodejs ./dist/static ./dist/static
COPY --chown=nextjs:nodejs ./public ./public

# Add proxy configuration support
ARG PROXY_URL
ENV PROXY_URL=$PROXY_URL
ARG HTTP_PROXY
ENV HTTP_PROXY=$HTTP_PROXY
ARG HTTPS_PROXY
ENV HTTPS_PROXY=$HTTPS_PROXY

# Configure proxychains if proxy is set - DISABLED to avoid errors
# RUN if [ -n "$PROXY_URL" ]; then \
#         echo "strict_chain" > /etc/proxychains/proxychains.conf && \
#         echo "proxy_dns" >> /etc/proxychains/proxychains.conf && \
#         echo "tcp_read_time_out 15000" >> /etc/proxychains/proxychains.conf && \
#         echo "tcp_connect_time_out 8000" >> /etc/proxychains/proxychains.conf && \
#         echo "[ProxyList]" >> /etc/proxychains/proxychains.conf && \
#         echo "http $(echo $PROXY_URL | sed 's|http://||' | tr ':' ' ')" >> /etc/proxychains/proxychains.conf; \
#     fi

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

# Proxy functionality disabled to avoid proxychains errors
# Use standard HTTP_PROXY/HTTPS_PROXY environment variables instead
CMD node server.js
