FROM node:18-alpine AS base

FROM base AS deps

RUN apk add --no-cache libc6-compat python3 py3-pip make g++
RUN ln -sf python3 /usr/bin/python

WORKDIR /app

COPY package.json yarn.lock ./

RUN yarn config set registry 'https://registry.npmmirror.com/'
RUN yarn install --ignore-optional

FROM base AS builder

RUN apk update && apk add --no-cache git

ENV OPENAI_API_KEY=""
ENV GOOGLE_API_KEY=""
ENV CODE=""

WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

RUN yarn build

FROM base AS runner
WORKDIR /app

# RUN apk add proxychains-ng  # Disabled to avoid proxychains errors

# 代理配置
ENV PROXY_URL=""

# API密钥配置
ENV OPENAI_API_KEY=""
ENV GOOGLE_API_KEY=""
ENV DEEPSEEK_API_KEY=""

# 应用配置
ENV CODE=""
ENV ENABLE_MCP=""

COPY --from=builder /app/public ./public
COPY --from=builder /app/dist/standalone ./
COPY --from=builder /app/dist/static ./dist/static
COPY --from=builder /app/dist/server ./dist/server

RUN mkdir -p /app/app/mcp && chmod 777 /app/app/mcp
COPY --from=builder /app/app/mcp/mcp_config.default.json /app/app/mcp/mcp_config.json

EXPOSE 3000

# Proxy functionality disabled to avoid proxychains errors
# Use application-level proxy configuration instead
CMD ["node", "server.js"]
