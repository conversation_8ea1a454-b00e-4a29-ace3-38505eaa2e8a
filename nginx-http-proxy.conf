# Nginx HTTP 代理配置
# 用于解决 DeepSeek API 区域限制问题
# 
# 使用方法：
# 1. 复制此文件到 /etc/nginx/sites-available/deepseek-http-proxy
# 2. 创建软链接：sudo ln -s /etc/nginx/sites-available/deepseek-http-proxy /etc/nginx/sites-enabled/
# 3. 测试配置：sudo nginx -t
# 4. 重启 nginx：sudo systemctl restart nginx
# 5. 在 .env.local 中设置：PROXY_URL=http://localhost:3000/deepseek-proxy

server {
    listen 3000;
    server_name localhost;
    
    # 日志配置
    access_log /var/log/nginx/deepseek_http_proxy.log;
    error_log /var/log/nginx/deepseek_http_proxy_error.log;
    
    # 简化的 HTTP 代理 - 直接转发到 DeepSeek API
    location / {
        # 解析请求的目标URL
        resolver *******;

        # 设置代理目标
        set $target_host "api.deepseek.com";
        set $target_scheme "https";

        # 构建完整的代理URL
        proxy_pass $target_scheme://$target_host$request_uri;

        # 代理请求头配置
        proxy_pass_request_headers on;
        proxy_set_header Host $target_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # SSL 配置
        proxy_ssl_verify off;
        proxy_ssl_server_name on;

        # 超时配置
        proxy_connect_timeout 30s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;

        # 支持流式响应
        proxy_buffering off;
        proxy_request_buffering off;
        proxy_http_version 1.1;
        proxy_set_header Connection "";

        # CORS 支持
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
        add_header Access-Control-Allow-Headers "Content-Type, Authorization";

        # 处理 OPTIONS 请求
        if ($request_method = 'OPTIONS') {
            return 204;
        }
    }
    
    # 健康检查
    location /health {
        return 200 "HTTP Proxy OK";
        add_header Content-Type text/plain;
    }
}
