# Nginx 代理配置示例 - DeepSeek API 专用
# 用于解决 DeepSeek API 区域限制问题
#
# 使用方法：
# 1. 复制此文件到 /etc/nginx/sites-available/deepseek-proxy
# 2. 创建软链接：sudo ln -s /etc/nginx/sites-available/deepseek-proxy /etc/nginx/sites-enabled/
# 3. 测试配置：sudo nginx -t
# 4. 重启 nginx：sudo systemctl restart nginx
# 5. 在 .env.local 中设置：PROXY_URL=http://localhost:3001

server {
    listen 3001;
    server_name localhost;

    # 日志配置
    access_log /var/log/nginx/deepseek_proxy.log;
    error_log /var/log/nginx/deepseek_proxy_error.log;

    # 只代理 DeepSeek API 的聊天接口
    location /chat/completions {
        proxy_pass https://api.deepseek.com/chat/completions;

        # 保持原始请求头（包括 Authorization）
        proxy_pass_request_headers on;
        proxy_set_header Host api.deepseek.com;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # SSL 配置
        proxy_ssl_verify off;
        proxy_ssl_server_name on;

        # 超时配置
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # 支持流式响应
        proxy_buffering off;
        proxy_request_buffering off;
        proxy_http_version 1.1;
        proxy_set_header Connection "";
    }

    # 代理模型列表接口（如果需要）
    location /models {
        proxy_pass https://api.deepseek.com/models;

        proxy_pass_request_headers on;
        proxy_set_header Host api.deepseek.com;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_ssl_verify off;
        proxy_ssl_server_name on;

        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # 健康检查
    location /health {
        return 200 "DeepSeek Proxy OK";
        add_header Content-Type text/plain;
    }

    # 拒绝其他所有请求
    location / {
        return 404 "Not Found - Only DeepSeek API endpoints are proxied";
        add_header Content-Type text/plain;
    }
}
