#!/bin/bash

# 阿里云服务器 Nginx 代理自动配置脚本
# 用于解决 DeepSeek API 区域限制问题

set -e

echo "=== NextChat Nginx 代理配置脚本 ==="
echo "此脚本将在阿里云服务器上配置 Nginx 作为 DeepSeek API 代理"
echo

# 检查是否为 root 用户
if [ "$EUID" -ne 0 ]; then
    echo "请使用 sudo 运行此脚本"
    exit 1
fi

# 检测操作系统
if [ -f /etc/redhat-release ]; then
    OS="centos"
    NGINX_CONF_DIR="/etc/nginx/conf.d"
elif [ -f /etc/debian_version ]; then
    OS="ubuntu"
    NGINX_CONF_DIR="/etc/nginx/conf.d"
else
    echo "不支持的操作系统"
    exit 1
fi

echo "检测到操作系统: $OS"

# 安装 Nginx
echo "正在安装 Nginx..."
if [ "$OS" = "centos" ]; then
    yum update -y
    yum install -y nginx
elif [ "$OS" = "ubuntu" ]; then
    apt update
    apt install -y nginx
fi

# 创建配置目录
mkdir -p $NGINX_CONF_DIR
mkdir -p /var/log/nginx

# 创建 Nginx 配置文件
echo "正在创建 Nginx 配置..."
cat > $NGINX_CONF_DIR/deepseek-proxy.conf << 'EOF'
server {
    listen 8080;
    server_name localhost;
    
    # 日志配置
    access_log /var/log/nginx/deepseek_access.log;
    error_log /var/log/nginx/deepseek_error.log;
    
    # 代理 DeepSeek API
    location /v1/ {
        # 直接代理到 DeepSeek API
        proxy_pass https://api.deepseek.com;
        
        # 保持原始请求头
        proxy_set_header Host api.deepseek.com;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 传递所有请求头（包括 Authorization）
        proxy_pass_request_headers on;
        
        # SSL 配置
        proxy_ssl_verify off;
        proxy_ssl_server_name on;
        proxy_ssl_protocols TLSv1.2 TLSv1.3;
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # 缓冲设置（适合流式响应）
        proxy_buffering off;
        proxy_request_buffering off;
        proxy_cache off;
        
        # 支持 WebSocket 和流式传输
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # 添加 CORS 头（如果需要）
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
        add_header Access-Control-Allow-Headers "Authorization, Content-Type";
        
        # 处理 OPTIONS 请求
        if ($request_method = 'OPTIONS') {
            return 204;
        }
    }
    
    # 健康检查端点
    location /health {
        access_log off;
        return 200 "nginx proxy is running";
        add_header Content-Type text/plain;
    }
}
EOF

# 测试 Nginx 配置
echo "正在测试 Nginx 配置..."
nginx -t

if [ $? -ne 0 ]; then
    echo "Nginx 配置测试失败"
    exit 1
fi

# 启动并启用 Nginx
echo "正在启动 Nginx..."
systemctl start nginx
systemctl enable nginx

# 配置防火墙（如果存在）
echo "正在配置防火墙..."
if command -v firewall-cmd &> /dev/null; then
    # CentOS/RHEL firewalld
    firewall-cmd --permanent --add-port=8080/tcp
    firewall-cmd --reload
    echo "已开放端口 8080 (firewalld)"
elif command -v ufw &> /dev/null; then
    # Ubuntu UFW
    ufw allow 8080/tcp
    echo "已开放端口 8080 (ufw)"
else
    echo "未检测到防火墙，请手动开放端口 8080"
fi

# 测试代理是否工作
echo "正在测试代理连接..."
sleep 2

if curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/health | grep -q "200"; then
    echo "✅ Nginx 代理配置成功！"
else
    echo "❌ Nginx 代理测试失败"
    echo "请检查日志: tail -f /var/log/nginx/deepseek_error.log"
    exit 1
fi

# 显示配置信息
echo
echo "=== 配置完成 ==="
echo "Nginx 代理地址: http://localhost:8080"
echo "健康检查: http://localhost:8080/health"
echo
echo "请在 NextChat 的 dist/.env.local 中配置:"
echo "DEEPSEEK_URL=http://localhost:8080/v1"
echo "# 或者如果使用 Docker:"
echo "# DEEPSEEK_URL=http://host.docker.internal:8080/v1"
echo
echo "常用命令:"
echo "  查看状态: systemctl status nginx"
echo "  重启服务: systemctl restart nginx"
echo "  查看日志: tail -f /var/log/nginx/deepseek_access.log"
echo "  测试配置: nginx -t"
echo
echo "如果遇到问题，请检查:"
echo "1. 防火墙是否开放端口 8080"
echo "2. SELinux 是否影响代理 (setenforce 0)"
echo "3. 网络连接是否正常"
echo
echo "配置完成！现在可以重新部署 NextChat 了。"