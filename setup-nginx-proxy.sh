#!/bin/bash

# NextChat Nginx 代理设置脚本
# 用于解决 DeepSeek API 区域限制问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [ "$EUID" -ne 0 ]; then
        log_error "请使用 root 用户或 sudo 运行此脚本"
        exit 1
    fi
}

# 检查nginx是否安装
check_nginx() {
    log_info "检查 Nginx 安装状态..."
    
    if ! command -v nginx &> /dev/null; then
        log_warning "Nginx 未安装，正在安装..."
        
        # 检测系统类型
        if [ -f /etc/debian_version ]; then
            apt update && apt install -y nginx
        elif [ -f /etc/redhat-release ]; then
            yum install -y nginx || dnf install -y nginx
        else
            log_error "不支持的系统类型，请手动安装 Nginx"
            exit 1
        fi
        
        log_success "Nginx 安装完成"
    else
        log_success "Nginx 已安装"
    fi
}

# 配置nginx代理
setup_nginx_proxy() {
    log_info "配置 Nginx 代理..."
    
    # 创建代理配置文件
    cat > /etc/nginx/sites-available/deepseek-proxy << 'EOF'
server {
    listen 8080;
    server_name localhost;
    
    access_log /var/log/nginx/deepseek_proxy_access.log;
    error_log /var/log/nginx/deepseek_proxy_error.log;
    
    location / {
        proxy_pass https://api.deepseek.com;
        
        proxy_set_header Host api.deepseek.com;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 添加香港地区标识
        proxy_set_header CF-IPCountry HK;
        proxy_set_header CF-Connecting-IP *************;
        
        proxy_ssl_verify off;
        proxy_ssl_server_name on;
        
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        proxy_buffering off;
        proxy_request_buffering off;
        
        proxy_http_version 1.1;
        proxy_set_header Connection "";
    }
    
    location /health {
        return 200 "Nginx Proxy OK";
        add_header Content-Type text/plain;
    }
}
EOF
    
    # 启用站点
    ln -sf /etc/nginx/sites-available/deepseek-proxy /etc/nginx/sites-enabled/
    
    log_success "Nginx 代理配置完成"
}

# 测试nginx配置
test_nginx_config() {
    log_info "测试 Nginx 配置..."
    
    if nginx -t; then
        log_success "Nginx 配置测试通过"
    else
        log_error "Nginx 配置测试失败"
        exit 1
    fi
}

# 启动nginx服务
start_nginx() {
    log_info "启动 Nginx 服务..."
    
    systemctl enable nginx
    systemctl restart nginx
    
    if systemctl is-active --quiet nginx; then
        log_success "Nginx 服务启动成功"
    else
        log_error "Nginx 服务启动失败"
        exit 1
    fi
}

# 配置防火墙
configure_firewall() {
    log_info "配置防火墙..."
    
    # 检查防火墙类型并开放端口
    if command -v ufw &> /dev/null; then
        ufw allow 8080/tcp
        log_success "UFW 防火墙规则已添加"
    elif command -v firewall-cmd &> /dev/null; then
        firewall-cmd --permanent --add-port=8080/tcp
        firewall-cmd --reload
        log_success "Firewalld 防火墙规则已添加"
    else
        log_warning "未检测到防火墙，请手动开放 8080 端口"
    fi
}

# 创建环境变量配置
create_env_config() {
    log_info "创建环境变量配置..."
    
    # 检查是否存在 .env.local 文件
    if [ ! -f ".env.local" ]; then
        cat > .env.local << EOF
# DeepSeek API 配置
DEEPSEEK_API_KEY=your-deepseek-api-key-here

# 代理配置 - 使用本地 Nginx 代理
PROXY_URL=http://localhost:8080

# 其他配置
# CODE=your-access-code
EOF
        log_success "已创建 .env.local 配置文件"
        log_warning "请编辑 .env.local 文件，设置你的 DEEPSEEK_API_KEY"
    else
        log_info ".env.local 文件已存在，请手动添加以下配置："
        echo "PROXY_URL=http://localhost:8080"
    fi
}

# 测试代理连接
test_proxy() {
    log_info "测试代理连接..."
    
    sleep 2  # 等待nginx启动
    
    # 测试健康检查端点
    if curl -s http://localhost:8080/health > /dev/null; then
        log_success "代理健康检查通过"
    else
        log_error "代理健康检查失败"
        return 1
    fi
    
    # 如果有API密钥，测试实际连接
    if [ -f ".env.local" ] && grep -q "DEEPSEEK_API_KEY=" .env.local; then
        api_key=$(grep "DEEPSEEK_API_KEY=" .env.local | cut -d'=' -f2)
        if [ "$api_key" != "your-deepseek-api-key-here" ] && [ -n "$api_key" ]; then
            log_info "测试 DeepSeek API 连接..."
            
            response=$(curl -s -w "HTTPSTATUS:%{http_code}" \
                -H "Content-Type: application/json" \
                -H "Authorization: Bearer $api_key" \
                -d '{"model":"deepseek-chat","messages":[{"role":"user","content":"test"}],"max_tokens":1}' \
                http://localhost:8080/chat/completions)
            
            status=$(echo "$response" | tr -d '\n' | sed -E 's/.*HTTPSTATUS:([0-9]{3})$/\1/')
            
            if [ "$status" = "200" ]; then
                log_success "DeepSeek API 连接测试成功"
            else
                log_warning "DeepSeek API 连接测试失败，状态码: $status"
                log_info "这可能是由于 API 密钥问题，请检查配置"
            fi
        fi
    fi
}

# 显示完成信息
show_completion_info() {
    log_success "Nginx 代理设置完成！"
    echo ""
    echo "配置信息："
    echo "- 代理地址: http://localhost:8080"
    echo "- 健康检查: http://localhost:8080/health"
    echo "- 配置文件: /etc/nginx/sites-available/deepseek-proxy"
    echo "- 日志文件: /var/log/nginx/deepseek_proxy_*.log"
    echo ""
    echo "使用方法："
    echo "1. 编辑 .env.local 文件，设置你的 DEEPSEEK_API_KEY"
    echo "2. 确保 PROXY_URL=http://localhost:8080 已配置"
    echo "3. 重启你的 NextChat 应用"
    echo ""
    echo "管理命令："
    echo "- 重启代理: sudo systemctl restart nginx"
    echo "- 查看日志: sudo tail -f /var/log/nginx/deepseek_proxy_*.log"
    echo "- 测试配置: sudo nginx -t"
}

# 主函数
main() {
    echo "========================================"
    echo "NextChat Nginx 代理设置脚本"
    echo "========================================"
    echo ""
    
    check_root
    check_nginx
    setup_nginx_proxy
    test_nginx_config
    start_nginx
    configure_firewall
    create_env_config
    test_proxy
    show_completion_info
}

# 运行主函数
main "$@"
