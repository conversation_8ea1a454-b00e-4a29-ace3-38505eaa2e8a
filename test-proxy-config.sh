#!/bin/bash

# NextChat 代理配置测试脚本
# 用于验证代理配置是否正确读取和工作

set -e

echo "🔍 NextChat 代理配置测试"
echo "=============================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查环境变量文件
check_env_files() {
    log_info "检查环境变量文件..."
    
    if [ -f ".env.local" ]; then
        log_success "找到 .env.local 文件"
        ENV_FILE=".env.local"
    elif [ -f ".env" ]; then
        log_success "找到 .env 文件"
        ENV_FILE=".env"
    else
        log_warning "未找到 .env.local 或 .env 文件"
        log_info "请复制 .env.local.example 为 .env.local 并配置相应的值"
        return 1
    fi
    
    echo
}

# 读取代理配置
read_proxy_config() {
    log_info "读取代理配置..."
    
    if [ -f "$ENV_FILE" ]; then
        # 读取代理配置
        PROXY_URL=$(grep "^PROXY_URL=" "$ENV_FILE" 2>/dev/null | cut -d '=' -f2 | tr -d '"' || echo "")
        HTTP_PROXY=$(grep "^HTTP_PROXY=" "$ENV_FILE" 2>/dev/null | cut -d '=' -f2 | tr -d '"' || echo "")
        HTTPS_PROXY=$(grep "^HTTPS_PROXY=" "$ENV_FILE" 2>/dev/null | cut -d '=' -f2 | tr -d '"' || echo "")
        DEEPSEEK_API_KEY=$(grep "^DEEPSEEK_API_KEY=" "$ENV_FILE" 2>/dev/null | cut -d '=' -f2 | tr -d '"' || echo "")
        
        # 显示配置状态
        if [ -n "$PROXY_URL" ]; then
            log_success "PROXY_URL: $PROXY_URL"
        else
            log_warning "PROXY_URL: 未配置"
        fi
        
        if [ -n "$HTTP_PROXY" ]; then
            log_success "HTTP_PROXY: $HTTP_PROXY"
        else
            log_warning "HTTP_PROXY: 未配置"
        fi
        
        if [ -n "$HTTPS_PROXY" ]; then
            log_success "HTTPS_PROXY: $HTTPS_PROXY"
        else
            log_warning "HTTPS_PROXY: 未配置"
        fi
        
        if [ -n "$DEEPSEEK_API_KEY" ]; then
            log_success "DEEPSEEK_API_KEY: 已配置 (${DEEPSEEK_API_KEY:0:10}...)"
        else
            log_error "DEEPSEEK_API_KEY: 未配置"
        fi
    fi
    
    echo
}

# 测试代理连接
test_proxy_connection() {
    log_info "测试代理连接..."
    
    # 确定使用的代理
    ACTIVE_PROXY=""
    if [ -n "$PROXY_URL" ]; then
        ACTIVE_PROXY="$PROXY_URL"
    elif [ -n "$HTTPS_PROXY" ]; then
        ACTIVE_PROXY="$HTTPS_PROXY"
    elif [ -n "$HTTP_PROXY" ]; then
        ACTIVE_PROXY="$HTTP_PROXY"
    fi
    
    if [ -n "$ACTIVE_PROXY" ]; then
        log_info "使用代理: $ACTIVE_PROXY"
        
        # 测试代理连接到 DeepSeek API
        if command -v curl >/dev/null 2>&1; then
            log_info "测试通过代理访问 DeepSeek API..."
            
            if curl --proxy "$ACTIVE_PROXY" -s --max-time 10 -o /dev/null -w "%{http_code}" "https://api.deepseek.com" | grep -q "200\|301\|302\|403\|404"; then
                log_success "代理连接测试成功"
            else
                log_error "代理连接测试失败"
                log_info "请检查代理服务器是否正常运行"
            fi
        else
            log_warning "curl 命令不可用，跳过连接测试"
        fi
    else
        log_warning "未配置代理，将使用直连"
        
        # 测试直连
        if command -v curl >/dev/null 2>&1; then
            log_info "测试直连访问 DeepSeek API..."
            
            if curl -s --max-time 10 -o /dev/null -w "%{http_code}" "https://api.deepseek.com" | grep -q "200\|301\|302\|403\|404"; then
                log_success "直连测试成功"
            else
                log_warning "直连测试失败，可能需要配置代理"
            fi
        fi
    fi
    
    echo
}

# 检查依赖包
check_dependencies() {
    log_info "检查代理相关依赖包..."
    
    if [ -f "package.json" ]; then
        if grep -q "http-proxy-agent" package.json; then
            log_success "http-proxy-agent: 已安装"
        else
            log_error "http-proxy-agent: 未安装"
        fi
        
        if grep -q "https-proxy-agent" package.json; then
            log_success "https-proxy-agent: 已安装"
        else
            log_error "https-proxy-agent: 未安装"
        fi
        
        if grep -q "socks-proxy-agent" package.json; then
            log_success "socks-proxy-agent: 已安装"
        else
            log_warning "socks-proxy-agent: 未安装 (SOCKS代理需要)"
        fi
    else
        log_error "package.json 文件不存在"
    fi
    
    echo
}

# 提供配置建议
provide_suggestions() {
    log_info "配置建议:"
    
    if [ -z "$PROXY_URL" ] && [ -z "$HTTP_PROXY" ] && [ -z "$HTTPS_PROXY" ]; then
        echo "  1. 如果遇到区域限制错误，请配置代理:"
        echo "     PROXY_URL=http://localhost:7890"
        echo "  2. 或使用系统级代理:"
        echo "     HTTP_PROXY=http://localhost:7890"
        echo "     HTTPS_PROXY=http://localhost:7890"
    fi
    
    if [ -z "$DEEPSEEK_API_KEY" ]; then
        echo "  3. 请配置 DeepSeek API 密钥:"
        echo "     DEEPSEEK_API_KEY=sk-your-api-key"
    fi
    
    echo "  4. 参考文档:"
    echo "     - PROXY_SETUP.md: 代理配置指南"
    echo "     - .env.local.example: 配置示例"
    
    echo
}

# 主函数
main() {
    check_env_files || exit 1
    read_proxy_config
    test_proxy_connection
    check_dependencies
    provide_suggestions
    
    log_success "代理配置测试完成"
}

# 运行主函数
main