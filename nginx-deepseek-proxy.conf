# Nginx 配置文件：DeepSeek API 代理
# 将此文件放置到 /etc/nginx/conf.d/ 目录下

server {
    listen 8080;
    server_name localhost;
    
    # 日志配置
    access_log /var/log/nginx/deepseek_access.log;
    error_log /var/log/nginx/deepseek_error.log;
    
    # 代理 DeepSeek API
    location /v1/ {
        # 直接代理到 DeepSeek API
        proxy_pass https://api.deepseek.com;
        
        # 保持原始请求头
        proxy_set_header Host api.deepseek.com;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 传递所有请求头（包括 Authorization）
        proxy_pass_request_headers on;
        
        # SSL 配置
        proxy_ssl_verify off;
        proxy_ssl_server_name on;
        proxy_ssl_protocols TLSv1.2 TLSv1.3;
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # 缓冲设置（适合流式响应）
        proxy_buffering off;
        proxy_request_buffering off;
        proxy_cache off;
        
        # 支持 WebSocket 和流式传输
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # 添加 CORS 头（如果需要）
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
        add_header Access-Control-Allow-Headers "Authorization, Content-Type";
        
        # 处理 OPTIONS 请求
        if ($request_method = 'OPTIONS') {
            return 204;
        }
    }
    
    # 健康检查端点
    location /health {
        access_log off;
        return 200 "nginx proxy is running";
        add_header Content-Type text/plain;
    }
}