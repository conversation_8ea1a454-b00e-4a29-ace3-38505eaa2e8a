# 解决 DeepSeek API 区域限制问题

如果你在使用 NextChat 时遇到 `unsupported_country_region_territory` 错误，这表示你所在的地区不支持直接访问 DeepSeek API。本文档将指导你如何通过配置代理来解决这个问题。

## 🚨 常见错误信息

```
Error: unsupported_country_region_territory
```

或者在浏览器控制台看到类似的区域限制错误。

## 🔧 解决方案

### 方案一：配置应用级代理（推荐）

1. **复制配置文件**
   ```bash
   cp .env.local.example .env.local
   ```

2. **编辑 `.env.local` 文件**
   ```bash
   # 取消注释并设置你的代理URL
   DEEPSEEK_API_KEY=sk-your-deepseek-api-key-here
   PROXY_URL=http://localhost:7890
   ```

3. **支持的代理协议**
   - HTTP: `http://proxy.example.com:8080`
   - HTTPS: `https://proxy.example.com:8080`
   - SOCKS5: `socks5://proxy.example.com:1080`

### 方案二：配置系统级代理

在 `.env.local` 文件中设置：
```bash
DEEPSEEK_API_KEY=sk-your-deepseek-api-key-here
HTTP_PROXY=http://localhost:7890
HTTPS_PROXY=http://localhost:7890
```

### 方案三：使用反向代理

如果你有自己的反向代理服务器，可以设置：
```bash
DEEPSEEK_API_KEY=sk-your-deepseek-api-key-here
DEEPSEEK_URL=https://your-reverse-proxy.com/v1
```

## 🧪 测试配置

运行测试脚本验证配置是否正确：
```bash
./test-proxy-config.sh
```

这个脚本会：
- 检查环境变量文件
- 验证代理配置
- 测试代理连接
- 提供配置建议

## 📋 常见代理软件配置

### Clash
- 默认端口：`http://localhost:7890`
- 配置示例：`PROXY_URL=http://localhost:7890`

### V2Ray
- 默认端口：`http://localhost:10809`
- 配置示例：`PROXY_URL=http://localhost:10809`

### Shadowsocks
- 默认端口：`socks5://localhost:1080`
- 配置示例：`PROXY_URL=socks5://localhost:1080`

### 自定义代理
- 根据你的代理软件设置相应的地址和端口

## 🐳 Docker 部署

如果你使用 Docker 部署，代理配置会自动传递给容器：

```bash
# 构建镜像（自动读取代理配置）
./build.sh

# 部署容器（自动读取代理配置）
./deploy.sh
```

或者手动指定代理：
```bash
./deploy.sh --proxy-url http://localhost:7890
```

## 🔍 故障排除

### 1. 代理配置未生效

**检查步骤：**
- 确认 `.env.local` 文件存在且格式正确
- 验证代理URL格式：`protocol://host:port`
- 运行测试脚本：`./test-proxy-config.sh`

### 2. 代理连接失败

**可能原因：**
- 代理服务器未启动
- 代理地址或端口错误
- 防火墙阻止连接

**解决方法：**
```bash
# 测试代理连接
curl --proxy http://localhost:7890 https://api.deepseek.com

# 检查代理服务状态
netstat -an | grep 7890
```

### 3. Docker 容器无法使用代理

**解决方法：**
- 确保代理服务器允许容器网络访问
- 使用 `host.docker.internal` 替代 `localhost`：
  ```bash
  PROXY_URL=http://host.docker.internal:7890
  ```

### 4. 仍然出现区域限制错误

**检查清单：**
- [ ] 代理服务器是否位于支持的地区
- [ ] 代理服务器是否正常工作
- [ ] 环境变量是否正确设置
- [ ] 应用是否重启以加载新配置

## 📚 相关文档

- [PROXY_SETUP.md](./PROXY_SETUP.md) - 详细的代理配置指南
- [DEPLOY.md](./DEPLOY.md) - 部署文档
- [.env.local.example](./.env.local.example) - 配置示例文件

## 💡 最佳实践

1. **优先使用 PROXY_URL**：比系统级代理更精确
2. **选择稳定的代理服务器**：确保位于支持的地区
3. **定期测试代理连接**：使用测试脚本验证
4. **备份配置文件**：避免意外丢失配置
5. **监控日志**：查看代理使用情况和错误信息

## 🆘 获取帮助

如果以上方法都无法解决问题，请：

1. 运行测试脚本并提供输出：`./test-proxy-config.sh`
2. 检查浏览器控制台的错误信息
3. 查看服务器日志中的代理相关信息
4. 在 [GitHub Issues](https://github.com/ChatGPTNextWeb/NextChat/issues) 中搜索相关问题或创建新的 issue

---

**注意**：使用代理时请遵守当地法律法规，确保代理服务的合法性和安全性。