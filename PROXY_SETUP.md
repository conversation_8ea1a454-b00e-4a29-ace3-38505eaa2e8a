# NextChat 代理配置自动化指南

本指南介绍如何在 NextChat 中自动化配置代理设置，解决地区限制问题。

## 🚀 快速开始

### 1. 配置环境变量

```bash
# 复制示例配置文件
cp .env.local.example .env.local

# 编辑配置文件
vim .env.local  # 或使用其他编辑器
```

### 2. 设置代理配置

在 `.env.local` 文件中取消注释并设置代理URL：

```bash
# 应用级代理URL (推荐)
PROXY_URL=http://localhost:7890

# 或者使用系统级代理
# HTTP_PROXY=http://localhost:7890
# HTTPS_PROXY=http://localhost:7890
```

### 3. 自动化部署

```bash
# 构建项目（自动读取代理配置）
./build.sh

# 部署到服务器（自动读取代理配置）
./deploy.sh
```

## 📋 支持的代理类型

| 环境变量 | 说明 | 示例 |
|---------|------|------|
| `PROXY_URL` | 应用级代理，支持 http/https/socks5 | `http://localhost:7890` |
| `HTTP_PROXY` | HTTP 请求代理 | `http://proxy.example.com:8080` |
| `HTTPS_PROXY` | HTTPS 请求代理 | `http://proxy.example.com:8080` |

## 🔧 配置优先级

1. **命令行参数** (最高优先级)
   ```bash
   ./deploy.sh --proxy-url http://localhost:7890
   ```

2. **环境变量文件** (.env.local > .env)
   ```bash
   PROXY_URL=http://localhost:7890
   ```

## 🛠 自动化特性

### 构建脚本 (build.sh)
- ✅ 自动从 `.env.local` 或 `.env` 读取代理配置
- ✅ 将代理配置传递给 Docker 构建过程
- ✅ 显示检测到的代理配置状态
- ✅ 在 Docker 镜像中包含代理支持

### 部署脚本 (deploy.sh)
- ✅ 自动从 `.env.local` 或 `.env` 读取代理配置
- ✅ 支持命令行参数覆盖环境变量
- ✅ 将代理配置传递给 Docker 容器
- ✅ 显示代理配置状态和帮助信息

## 📝 使用示例

### 示例 1: 使用本地代理

```bash
# .env.local
DEEPSEEK_API_KEY=sk-your-api-key
PROXY_URL=http://localhost:7890

# 构建和部署
./build.sh
./deploy.sh
```

### 示例 2: 使用远程代理

```bash
# .env.local
DEEPSEEK_API_KEY=sk-your-api-key
PROXY_URL=http://proxy.example.com:8080

# 构建和部署
./build.sh
./deploy.sh
```

### 示例 3: 命令行覆盖

```bash
# 即使 .env.local 中有配置，也可以通过命令行覆盖
./deploy.sh --proxy-url http://different-proxy.com:8080
```

## 🔍 验证配置

使用测试脚本验证代理配置读取：

```bash
./test-proxy-config.sh
```

## 🐛 故障排除

### 常见问题

1. **代理配置未生效**
   - 检查 `.env.local` 文件是否存在
   - 确认代理URL格式正确
   - 运行测试脚本验证配置读取

2. **地区限制错误**
   ```
   Error: unsupported_country_region_territory
   ```
   - 确保代理服务器正常运行
   - 验证代理URL可访问
   - 检查代理服务器地理位置

3. **Docker 容器无法访问代理**
   - 确保代理服务器允许容器网络访问
   - 检查防火墙设置
   - 验证 Docker 网络配置

### 调试命令

```bash
# 查看容器环境变量
docker exec nextchat-app env | grep PROXY

# 查看容器日志
docker logs nextchat-app -f

# 测试代理连接
curl --proxy http://localhost:7890 https://api.openai.com/v1/models
```

## 📚 相关文件

- `.env.example` - 环境变量示例文件
- `.env.local.example` - 包含代理配置的示例文件
- `build.sh` - 构建脚本（支持代理自动读取）
- `deploy.sh` - 部署脚本（支持代理自动读取）
- `test-proxy-config.sh` - 代理配置测试脚本

## 🔗 更多信息

- [Docker 代理配置](https://docs.docker.com/network/proxy/)
- [环境变量最佳实践](https://12factor.net/config)
- [NextChat 部署文档](./DEPLOY.md)