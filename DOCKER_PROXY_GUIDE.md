# Docker 代理配置指南

## 问题说明

之前的Docker配置使用了proxychains，但这会导致以下错误：
```
[proxychains] proxy localhost has invalid value or is not numeric
```

## 解决方案

现在已经移除了所有proxychains相关配置，改为使用应用程序级别的代理配置。

## 推荐的代理配置方法

### 1. 使用Docker的代理配置

在docker-compose.yml中配置：
```yaml
services:
  nextchat:
    environment:
      - HTTP_PROXY=http://your-proxy:port
      - HTTPS_PROXY=http://your-proxy:port
      - NO_PROXY=localhost,127.0.0.1
```

### 2. 使用Docker daemon代理

配置Docker daemon的代理设置：
```json
{
  "proxies": {
    "default": {
      "httpProxy": "http://your-proxy:port",
      "httpsProxy": "http://your-proxy:port",
      "noProxy": "localhost,127.0.0.1"
    }
  }
}
```

### 3. 使用网络级别代理

- 配置系统级别的代理
- 使用透明代理
- 使用VPN或其他网络解决方案

## 注意事项

1. **PROXY_URL环境变量**：虽然保留了这个变量，但现在它不会触发proxychains
2. **应用程序代理**：NextChat会尝试使用HTTP_PROXY等标准环境变量
3. **Edge Runtime限制**：由于使用了Edge Runtime，某些Node.js代理功能可能受限

## 验证配置

启动容器后，检查日志中是否还有proxychains相关的错误信息。如果没有，说明配置成功。