#!/bin/bash

# NextChat 内存优化构建脚本
# 专门解决 Docker 构建内存不足问题（exit code 137）

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Docker 内存配置
check_docker_memory() {
    log_info "检查 Docker 内存配置..."
    local total_memory=$(docker system info 2>/dev/null | grep "Total Memory" | awk '{print $3}' | sed 's/GiB//')
    
    if [ -n "$total_memory" ]; then
        local memory_gb=$(echo "$total_memory" | cut -d'.' -f1)
        log_info "Docker 可用内存: ${total_memory}GB"
        
        if [ "$memory_gb" -lt 4 ]; then
            log_warning "Docker 内存不足 (<4GB)，将使用内存优化构建策略"
            return 1
        fi
    fi
    return 0
}

# 内存优化构建策略
optimized_build() {
    log_info "使用内存优化构建策略..."
    
    # 1. 设置 Node.js 内存限制
    export NODE_OPTIONS="--max-old-space-size=1536"
    log_info "设置 Node.js 内存限制: 1536MB"
    
    # 2. 本地构建（避免 Docker 内存限制）
    log_info "开始 NextChat 本地构建流程..."
    
    # 检查环境依赖
    log_info "1. 检查环境依赖..."
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装"
        exit 1
    fi
    
    local node_version=$(node --version)
    log_info "Node.js 版本检查通过: $node_version"
    log_success "环境检查完成"
    
    # 检查环境变量
    log_info "2. 检查环境变量配置..."
    if [ ! -f ".env" ] && [ ! -f ".env.local" ]; then
        log_error "未找到环境变量文件"
        exit 1
    fi
    log_success "环境变量配置检查完成"
    
    # 清理旧文件
    log_info "清理旧文件..."
    rm -rf .next dist nextchat-*.tar.gz Dockerfile.optimized 2>/dev/null || true
    log_success "旧文件清理完成"
    
    # 安装依赖
    log_info "3. 安装项目依赖..."
    yarn install --frozen-lockfile
    log_success "依赖安装完成"
    
    # 执行构建
    log_info "4. 执行本地构建..."
    yarn build
    log_success "本地构建完成"
    
    # 5. 复制部署脚本到 dist 目录
    log_info "5. 复制部署脚本到 dist 目录..."
    mkdir -p dist
    if [ -f "deploy.sh" ]; then
        cp deploy.sh dist/
        chmod +x dist/deploy.sh
    fi
    log_success "部署脚本已复制"
    
    # 6. 构建 Docker 镜像
    log_info "6. 构建 Docker 镜像..."
    
    # 3. 创建轻量级 Dockerfile
    log_info "创建内存优化的 Dockerfile..."
    cat > Dockerfile.optimized << 'EOF'
FROM node:18-alpine AS base

# Production image, copy pre-built files
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

# Install proxychains for proxy support - DISABLED to avoid errors
# RUN apk add --no-cache proxychains-ng

# Create user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copy pre-built application
COPY --chown=nextjs:nodejs ./dist/standalone ./
COPY --chown=nextjs:nodejs ./dist/static ./dist/static
COPY --chown=nextjs:nodejs ./public ./public

# Add proxy configuration support
ARG PROXY_URL
ENV PROXY_URL=$PROXY_URL
ARG HTTP_PROXY
ENV HTTP_PROXY=$HTTP_PROXY
ARG HTTPS_PROXY
ENV HTTPS_PROXY=$HTTPS_PROXY

# Configure proxychains if proxy is set - DISABLED to avoid errors
# RUN if [ -n "$PROXY_URL" ]; then \
#         echo "strict_chain" > /etc/proxychains/proxychains.conf && \
#         echo "proxy_dns" >> /etc/proxychains/proxychains.conf && \
#         echo "tcp_read_time_out 15000" >> /etc/proxychains/proxychains.conf && \
#         echo "tcp_connect_time_out 8000" >> /etc/proxychains/proxychains.conf && \
#         echo "[ProxyList]" >> /etc/proxychains/proxychains.conf && \
#         echo "http $(echo $PROXY_URL | sed 's|http://||' | tr ':' ' ')" >> /etc/proxychains/proxychains.conf; \
#     fi

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

# Proxy functionality disabled to avoid proxychains errors
# Use standard HTTP_PROXY/HTTPS_PROXY environment variables instead
CMD node server.js
EOF
    log_success "内存优化 Dockerfile 已创建"
    
    # 4. 构建优化的 Docker 镜像
    log_info "构建内存优化的 Docker 镜像..."
    
    # 获取环境变量（优先从.env.local读取，忽略注释行）
    get_env_var() {
        local var_name=$1
        local value=""
        local found=false
        
        # 优先从.env.local读取
        if [ -f ".env.local" ]; then
            if grep -q "^${var_name}=" .env.local 2>/dev/null; then
                value=$(grep "^${var_name}=" .env.local 2>/dev/null | cut -d '=' -f2- || echo "")
                found=true
            fi
        fi
        
        # 如果.env.local中没有找到该变量，再从.env读取（忽略注释行）
        if [ "$found" = "false" ] && [ -f ".env" ]; then
            if grep -q "^${var_name}=" .env 2>/dev/null; then
                value=$(grep "^${var_name}=" .env 2>/dev/null | cut -d '=' -f2- || echo "")
            fi
        fi
        
        echo "$value"
    }
    
    DEEPSEEK_API_KEY=$(get_env_var "DEEPSEEK_API_KEY")
    PROXY_URL=$(get_env_var "PROXY_URL")
    HTTP_PROXY=$(get_env_var "HTTP_PROXY")
    HTTPS_PROXY=$(get_env_var "HTTPS_PROXY")
    
    # 显示代理配置
    if [ -n "$PROXY_URL" ] || [ -n "$HTTP_PROXY" ] || [ -n "$HTTPS_PROXY" ]; then
        log_info "检测到代理配置:"
        [ -n "$PROXY_URL" ] && echo "  PROXY_URL: $PROXY_URL"
        [ -n "$HTTP_PROXY" ] && echo "  HTTP_PROXY: $HTTP_PROXY"
        [ -n "$HTTPS_PROXY" ] && echo "  HTTPS_PROXY: $HTTPS_PROXY"
    fi
    
    # 构建镜像（使用预构建的文件，避免内存密集的构建步骤）
    docker build \
        --platform=linux/amd64 \
        --build-arg PROXY_URL="$PROXY_URL" \
        --build-arg HTTP_PROXY="$HTTP_PROXY" \
        --build-arg HTTPS_PROXY="$HTTPS_PROXY" \
        -t nextchat-optimized:latest \
        -f Dockerfile.optimized .
    
    log_success "内存优化 Docker 镜像构建完成"
    
    # 5. 导出镜像
    log_info "导出优化镜像..."
    docker save -o nextchat-optimized.tar nextchat-optimized:latest
    gzip nextchat-optimized.tar
    
    local file_size=$(du -h nextchat-optimized.tar.gz | cut -f1)
    log_success "优化镜像导出完成: nextchat-optimized.tar.gz ($file_size)"
    
    # 6. 复制到 dist 目录
    mkdir -p dist
    cp nextchat-optimized.tar.gz dist/
    cp deploy.sh dist/
    chmod +x dist/deploy.sh
    
    log_success "文件已复制到 dist 目录"
}

# 标准构建策略
standard_build() {
    log_info "使用标准构建策略..."
    ./build.sh
}

# 检查环境
check_environment() {
    if [ ! -f ".env" ] && [ ! -f ".env.local" ]; then
        log_error "未找到 .env 或 .env.local 文件"
        exit 1
    fi
    
    if [ ! -f ".env" ] && [ -f ".env.local" ]; then
        cp .env.local .env
        log_info "从 .env.local 复制配置到 .env"
    fi
}



# 快速部署功能
quick_deploy() {
    local api_key="$1"
    local port="$2"
    local proxy_url="$3"
    local container_name="nextchat"
    local image_name="nextchat-optimized:latest"
    
    if [ -z "$api_key" ]; then
        log_error "API 密钥不能为空"
        exit 1
    fi
    
    # 检查 Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    # 加载镜像
    local image_file="nextchat-optimized.tar"
    local compressed_file="nextchat-optimized.tar.gz"
    
    if ! docker images | grep -q "nextchat-optimized"; then
        if [ -f "$compressed_file" ]; then
            log_info "解压并加载镜像..."
            gunzip "$compressed_file"
            docker load -i "nextchat-optimized.tar"
        elif [ -f "$image_file" ]; then
            log_info "加载镜像..."
            docker load -i "$image_file"
        else
            log_error "未找到镜像文件，请先执行构建"
            exit 1
        fi
    fi
    
    # 停止现有容器
    if docker ps -a -q -f name="$container_name" | grep -q .; then
        log_info "停止现有容器: $container_name"
        docker stop "$container_name" 2>/dev/null || true
        docker rm "$container_name" 2>/dev/null || true
    fi
    
    # 启动新容器
    local docker_cmd="docker run -d -p $port:3000 -e DEEPSEEK_API_KEY='$api_key' --name $container_name --restart unless-stopped"
    
    if [ -n "$proxy_url" ]; then
        docker_cmd="$docker_cmd -e PROXY_URL='$proxy_url'"
        log_info "使用代理: $proxy_url"
    fi
    
    docker_cmd="$docker_cmd $image_name"
    
    log_info "启动容器，端口: $port"
    eval "$docker_cmd"
    
    sleep 3
    
    if docker ps | grep -q "$container_name"; then
        log_success "部署成功！访问地址: http://localhost:$port"
    else
        log_error "部署失败，查看日志:"
        docker logs "$container_name"
        exit 1
    fi
}

# 容器管理功能
manage_container() {
    local action="$1"
    local container_name="nextchat"
    
    case "$action" in
        "stop")
            if docker ps -q -f name="$container_name" | grep -q .; then
                docker stop "$container_name"
                docker rm "$container_name"
                log_success "容器已停止"
            else
                log_info "容器未运行"
            fi
            ;;
        "restart")
            if docker ps -a | grep -q "$container_name"; then
                docker restart "$container_name"
                log_success "容器已重启"
            else
                log_error "容器不存在"
            fi
            ;;
        "logs")
            if docker ps -a | grep -q "$container_name"; then
                docker logs --tail 50 "$container_name"
            else
                log_error "容器不存在"
            fi
            ;;
        "status")
            if docker ps | grep -q "$container_name"; then
                docker ps | grep "$container_name"
                echo
                docker stats --no-stream "$container_name"
            else
                log_warning "容器未运行"
            fi
            ;;
    esac
}

# 显示帮助
show_help() {
    echo "NextChat 集成构建部署脚本"
    echo
    echo "用法: $0 [命令] [选项]"
    echo
    echo "命令:"
    echo "  build                 构建应用（默认）"
    echo "  deploy                快速部署到 Docker"
    echo "  stop                  停止容器"
    echo "  restart               重启容器"
    echo "  logs                  查看容器日志"
    echo "  status                查看容器状态"
    echo
    echo "构建选项:"
    echo "  --no-optimize         跳过优化"
    echo "  --no-cleanup          跳过清理"
    echo "  --quiet               静默模式"
    echo
    echo "部署选项:"
    echo "  -k, --api-key KEY     DeepSeek API 密钥（必需）"
    echo "  -p, --port PORT       服务端口（默认: 3000）"
    echo "  -x, --proxy URL       代理地址"
    echo
    echo "示例:"
    echo "  $0                                    # 构建应用"
    echo "  $0 build --no-optimize                # 构建（跳过优化）"
    echo "  $0 deploy -k sk-your-key              # 部署到端口 3000"
    echo "  $0 deploy -k sk-your-key -p 8080      # 部署到端口 8080"
    echo "  $0 deploy -k sk-your-key -x http://proxy:7890  # 使用代理部署"
    echo "  $0 stop                               # 停止容器"
    echo "  $0 logs                               # 查看日志"
    echo
    echo "特性:"
    echo "  - 自动检测 Docker 内存配置"
    echo "  - 内存不足时使用优化构建策略"
    echo "  - 集成构建、部署、管理功能"
    echo "  - 支持代理配置"
}

# 解析参数
COMMAND="build"
API_KEY=""
PORT="3000"
PROXY_URL=""
BUILD_OPTIONS=()

while [[ $# -gt 0 ]]; do
    case $1 in
        build|deploy|stop|restart|logs|status)
            COMMAND="$1"
            shift
            ;;
        -k|--api-key)
            API_KEY="$2"
            shift 2
            ;;
        -p|--port)
            PORT="$2"
            shift 2
            ;;
        -x|--proxy)
            PROXY_URL="$2"
            shift 2
            ;;
        --no-optimize|--no-cleanup|--quiet)
            BUILD_OPTIONS+=("$1")
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 主入口函数
main_entry() {
    case "$COMMAND" in
        "build")
            build_main "${BUILD_OPTIONS[@]}"
            ;;
        "deploy")
            if [ -z "$API_KEY" ]; then
                log_error "部署需要 API 密钥，使用 -k 参数"
                exit 1
            fi
            quick_deploy "$API_KEY" "$PORT" "$PROXY_URL"
            ;;
        "stop"|"restart"|"logs"|"status")
            manage_container "$COMMAND"
            ;;
    esac
}

# 构建主函数
build_main() {
    local build_options=("$@")
    
    log_info "开始 NextChat 内存优化构建..."
    
    # 检查环境
    check_environment
    
    # 检查 Docker 内存
    if ! check_docker_memory; then
        # 内存不足，使用优化构建策略
        optimized_build
    else
        # 内存充足，使用标准构建流程
        standard_build
    fi
    
    # 显示结果
    log_success "构建完成！"
    echo
    log_info "📦 构建产物:"
    if [ -f "nextchat-optimized.tar.gz" ]; then
        local file_size=$(du -h nextchat-optimized.tar.gz | cut -f1)
        echo "  ✅ 优化镜像: nextchat-optimized.tar.gz ($file_size)"
    fi
    if [ -f "nextchat-custom.tar.gz" ]; then
        local file_size=$(du -h nextchat-custom.tar.gz | cut -f1)
        echo "  ✅ 标准镜像: nextchat-custom.tar.gz ($file_size)"
    fi
    echo "  ✅ 部署脚本: dist/deploy.sh"
    echo
    log_info "🚀 部署步骤:"
    echo "  1. 上传 dist 目录到服务器"
    echo "  2. 在服务器上运行: cd dist && ./deploy.sh"
    echo
}

# 执行主入口函数
main_entry