# 使用 Nginx 作为代理解决 DeepSeek API 区域限制问题

在阿里云服务器上，您可以使用 Nginx 作为反向代理来解决 DeepSeek API 的区域限制问题，而无需安装 Clash 等代理软件。

## 方案概述

Nginx 可以作为反向代理，将 NextChat 的 API 请求转发到 DeepSeek API，同时可以配置上游代理服务器来绕过区域限制。

## 配置步骤

### 1. 安装 Nginx

```bash
# CentOS/RHEL
sudo yum install nginx

# Ubuntu/Debian
sudo apt update
sudo apt install nginx
```

### 2. 创建 Nginx 代理配置

创建配置文件 `/etc/nginx/conf.d/deepseek-proxy.conf`：

```nginx
server {
    listen 8080;
    server_name localhost;
    
    # 代理 DeepSeek API
    location /v1/ {
        # 设置上游代理（如果有的话）
        # proxy_pass http://your-proxy-server:port;
        
        # 直接代理到 DeepSeek API
        proxy_pass https://api.deepseek.com;
        
        # 保持原始请求头
        proxy_set_header Host api.deepseek.com;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 传递认证头
        proxy_pass_request_headers on;
        
        # SSL 配置
        proxy_ssl_verify off;
        proxy_ssl_server_name on;
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # 缓冲设置
        proxy_buffering off;
        proxy_request_buffering off;
    }
}
```

### 3. 如果需要通过上游代理

如果您有可用的代理服务器，可以配置 Nginx 通过代理转发：

```nginx
server {
    listen 8080;
    server_name localhost;
    
    location /v1/ {
        # 通过代理服务器转发
        proxy_pass http://your-proxy-ip:proxy-port;
        
        # 设置代理请求的目标
        proxy_set_header Host api.deepseek.com;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto https;
        
        # 重写请求 URL
        rewrite ^/v1/(.*)$ https://api.deepseek.com/v1/$1 break;
        
        proxy_pass_request_headers on;
        proxy_ssl_verify off;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        proxy_buffering off;
    }
}
```

### 4. 启动 Nginx

```bash
# 测试配置
sudo nginx -t

# 启动 Nginx
sudo systemctl start nginx
sudo systemctl enable nginx

# 重新加载配置
sudo systemctl reload nginx
```

### 5. 修改 NextChat 配置

在 `dist/.env.local` 中修改 DeepSeek API 配置：

```bash
# 使用本地 Nginx 代理
DEEPSEEK_API_KEY=sk-your-api-key
DEEPSEEK_URL=http://localhost:8080/v1

# 或者如果 NextChat 运行在 Docker 中
# DEEPSEEK_URL=http://host.docker.internal:8080/v1
```

## Docker 部署配置

如果 NextChat 运行在 Docker 中，需要确保容器可以访问宿主机的 Nginx：

### 方法1：使用 host.docker.internal

```bash
# 在 dist/.env.local 中
DEEPSEEK_URL=http://host.docker.internal:8080/v1
```

### 方法2：使用 Docker 网络

```yaml
# docker-compose.yml
version: '3.8'
services:
  nextchat:
    image: your-nextchat-image
    ports:
      - "3000:3000"
    environment:
      - DEEPSEEK_URL=http://nginx-proxy:8080/v1
    networks:
      - nextchat-network
  
  nginx-proxy:
    image: nginx:alpine
    ports:
      - "8080:8080"
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
    networks:
      - nextchat-network

networks:
  nextchat-network:
    driver: bridge
```

## 测试配置

### 1. 测试 Nginx 代理

```bash
# 测试代理是否工作
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-your-api-key" \
  -d '{
    "model": "deepseek-chat",
    "messages": [{"role": "user", "content": "Hello"}],
    "max_tokens": 10
  }'
```

### 2. 检查 Nginx 日志

```bash
# 查看访问日志
sudo tail -f /var/log/nginx/access.log

# 查看错误日志
sudo tail -f /var/log/nginx/error.log
```

## 优势

1. **轻量级**：Nginx 比 Clash 等代理软件更轻量
2. **高性能**：Nginx 具有出色的并发处理能力
3. **灵活配置**：可以精确控制代理规则
4. **日志监控**：便于监控和调试
5. **SSL 终止**：可以处理 SSL/TLS 加密

## 注意事项

1. **防火墙**：确保端口 8080 在防火墙中开放
2. **SELinux**：如果启用了 SELinux，可能需要配置相应策略
3. **性能调优**：根据实际负载调整 Nginx 配置
4. **监控**：建议配置日志轮转和监控

## 故障排除

### 常见问题

1. **502 Bad Gateway**
   - 检查上游服务器是否可达
   - 检查代理配置是否正确

2. **连接超时**
   - 增加超时时间配置
   - 检查网络连接

3. **SSL 错误**
   - 设置 `proxy_ssl_verify off`
   - 检查 SSL 证书配置

### 调试命令

```bash
# 检查 Nginx 状态
sudo systemctl status nginx

# 检查配置语法
sudo nginx -t

# 查看端口占用
sudo netstat -tlnp | grep :8080

# 测试连接
telnet localhost 8080
```

这种方案特别适合阿里云等云服务器环境，无需安装额外的代理软件，利用 Nginx 的反向代理功能即可解决区域限制问题。