# DeepSeek API 代理配置

## 🚀 快速配置

### 1. 配置 Nginx 代理

```bash
# 复制配置文件
sudo cp nginx.conf.example /etc/nginx/sites-available/deepseek-proxy

# 启用站点
sudo ln -s /etc/nginx/sites-available/deepseek-proxy /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启 nginx
sudo systemctl restart nginx

# 开放端口
sudo ufw allow 3001/tcp
```

### 2. 配置环境变量

```bash
# 复制并编辑配置文件
cp .env.example .env.local
nano .env.local
```

设置以下配置：
```bash
DEEPSEEK_API_KEY=sk-your-deepseek-api-key-here
PROXY_URL=http://localhost:3001
```

### 3. 重启应用

```bash
npm run dev
```

## 🧪 测试

```bash
# 测试代理健康状态
curl http://localhost:3001/health

# 测试聊天接口
curl -X POST http://localhost:3001/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{"model":"deepseek-chat","messages":[{"role":"user","content":"test"}],"max_tokens":1}'
```

## 📋 代理说明

- **端口**: 3001
- **代理接口**: 
  - `/chat/completions` → `https://api.deepseek.com/chat/completions`
  - `/models` → `https://api.deepseek.com/models`
  - `/health` → 健康检查
- **其他请求**: 返回 404，确保安全

配置完成后，应用将通过 nginx 代理访问 DeepSeek API。
