#!/bin/bash

# NextChat DeepSeek 区域限制解决方案部署脚本
# 此脚本帮助解决 "unsupported_country_region_territory" 错误

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要的工具
check_requirements() {
    log_info "检查必要的工具..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_warning "docker-compose 未安装，将使用 docker compose"
    fi
    
    log_success "工具检查完成"
}

# 读取环境变量配置
load_env_config() {
    log_info "加载环境变量配置..."
    
    # 检查 .env 文件
    if [ -f ".env" ]; then
        log_info "发现 .env 文件，加载配置..."
        export $(grep -v '^#' .env | xargs)
    elif [ -f ".env.local" ]; then
        log_info "发现 .env.local 文件，加载配置..."
        export $(grep -v '^#' .env.local | xargs)
    else
        log_warning "未发现环境变量文件，将使用默认配置"
    fi
}

# 检查代理配置
check_proxy_config() {
    log_info "检查代理配置..."
    
    if [ -n "$PROXY_URL" ]; then
        log_success "发现应用级代理配置: $PROXY_URL"
        export DOCKER_PROXY_URL="$PROXY_URL"
    elif [ -n "$HTTP_PROXY" ] && [ -n "$HTTPS_PROXY" ]; then
        log_success "发现系统级代理配置: HTTP_PROXY=$HTTP_PROXY, HTTPS_PROXY=$HTTPS_PROXY"
        export DOCKER_HTTP_PROXY="$HTTP_PROXY"
        export DOCKER_HTTPS_PROXY="$HTTPS_PROXY"
    else
        log_warning "未配置代理，如果遇到区域限制错误，请配置代理"
    fi
}

# 检查API密钥
check_api_keys() {
    log_info "检查API密钥配置..."
    
    if [ -z "$DEEPSEEK_API_KEY" ]; then
        log_error "DEEPSEEK_API_KEY 未配置，请在 .env 文件中设置"
        exit 1
    fi
    
    log_success "API密钥配置检查完成"
}

# 创建docker-compose.yml文件
create_docker_compose() {
    log_info "创建 docker-compose.yml 文件..."
    
    cat > docker-compose.yml << EOF
version: '3.8'

services:
  nextchat:
    build: .
    container_name: nextchat-deepseek
    ports:
      - "3000:3000"
    environment:
      # API配置
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY:-}
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY:-}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY:-}
      
      # 代理配置
      - PROXY_URL=${DOCKER_PROXY_URL:-}
      - HTTP_PROXY=${DOCKER_HTTP_PROXY:-}
      - HTTPS_PROXY=${DOCKER_HTTPS_PROXY:-}
      
      # 应用配置
      - CODE=${CODE:-}
      - ENABLE_MCP=${ENABLE_MCP:-}
      - DEEPSEEK_URL=${DEEPSEEK_URL:-}
      
      # Node.js配置
      - NODE_ENV=production
    restart: unless-stopped
    volumes:
      - ./data:/app/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
EOF
    
    log_success "docker-compose.yml 文件创建完成"
}

# 构建和启动服务
deploy_service() {
    log_info "构建和启动服务..."
    
    # 停止现有服务
    if docker ps | grep -q nextchat-deepseek; then
        log_info "停止现有服务..."
        docker-compose down || docker compose down
    fi
    
    # 构建镜像
    log_info "构建Docker镜像..."
    docker-compose build --no-cache || docker compose build --no-cache
    
    # 启动服务
    log_info "启动服务..."
    docker-compose up -d || docker compose up -d
    
    log_success "服务启动完成"
}

# 检查服务状态
check_service_status() {
    log_info "检查服务状态..."
    
    # 等待服务启动
    sleep 10
    
    if docker ps | grep -q nextchat-deepseek; then
        log_success "服务运行正常"
        log_info "访问地址: http://localhost:3000"
        log_info "服务器访问地址: http://*************:3000"
    else
        log_error "服务启动失败，请检查日志"
        docker-compose logs || docker compose logs
        exit 1
    fi
}

# 显示使用说明
show_usage_info() {
    log_info "部署完成！使用说明："
    echo ""
    echo "1. 访问地址: http://localhost:3000 或 http://*************:3000"
    echo "2. 如果遇到区域限制错误，应用会自动重试"
    echo "3. 查看日志: docker-compose logs -f nextchat-deepseek"
    echo "4. 停止服务: docker-compose down"
    echo "5. 重启服务: docker-compose restart"
    echo ""
    echo "故障排除："
    echo "- 如果仍然遇到区域限制问题，请配置代理服务器"
    echo "- 在 .env 文件中设置 PROXY_URL=http://your-proxy:port"
    echo "- 重新运行此脚本: ./deploy-with-proxy.sh"
    echo ""
}

# 主函数
main() {
    log_info "开始部署 NextChat DeepSeek 区域限制解决方案..."
    
    check_requirements
    load_env_config
    check_proxy_config
    check_api_keys
    create_docker_compose
    deploy_service
    check_service_status
    show_usage_info
    
    log_success "部署完成！"
}

# 运行主函数
main "$@"
